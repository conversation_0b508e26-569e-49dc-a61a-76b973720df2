# NewsMonitor Dashboard

A Streamlit dashboard for viewing and analyzing news articles and their embeddings.

## Features

- **Articles Overview**: View all articles with filtering and sorting options
- **Article Search**: Search articles by content and source
- **ChromaDB Search**: Semantic search through article chunks
- **Statistics**: Visualize article distribution and sentiment analysis

## Installation

1. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Running the Dashboard

1. Make sure you're in the dashboard directory:
```bash
cd dashboard
```

2. Run the Streamlit app:
```bash
streamlit run app.py
```

3. Open your browser and navigate to the URL shown in the terminal (usually http://localhost:8501)

## Usage

### Articles Overview
- View all articles in a table format
- Filter by date range
- See key metrics like total articles and average sentiment

### Article Search
- Search articles by keywords in title or content
- Filter by source
- View full article content and metadata

### ChromaDB Search
- Perform semantic search through article chunks
- Adjust number of results
- View similarity scores and metadata

### Statistics
- View article distribution over time
- See source distribution
- Analyze sentiment distribution
- View ChromaDB statistics 