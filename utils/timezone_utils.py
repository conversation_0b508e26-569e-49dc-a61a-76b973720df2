"""
Timezone utilities for handling date and time conversions.
"""

import logging
import re
from typing import Optional, <PERSON><PERSON>
from datetime import datetime
from zoneinfo import ZoneInfo

import pytz
from dateutil import parser
from dateutil.tz import gettz

# Configure logger
logger = logging.getLogger(__name__)

# Common timezone abbreviations mapping
TIMEZONE_ABBR = {
    'EST': 'US/Eastern',
    'EDT': 'US/Eastern',
    'CST': 'US/Central',
    'CDT': 'US/Central',
    'MST': 'US/Mountain',
    'MDT': 'US/Mountain',
    'PST': 'US/Pacific',
    'PDT': 'US/Pacific',
}

# Create tzinfos dictionary for dateutil.parser
TZINFOS = {
    'EST': gettz('US/Eastern'),
    'EDT': gettz('US/Eastern'),
    'CST': gettz('US/Central'),
    'CDT': gettz('US/Central'),
    'MST': gettz('US/Mountain'),
    'MDT': gettz('US/Mountain'),
    'PST': gettz('US/Pacific'),
    'PDT': gettz('US/Pacific'),
}


def extract_timezone_from_date(date_str: str) -> Tuple[str, Optional[str]]:
    """
    Extract timezone information from a date string.

    Args:
        date_str: Date string that might contain timezone information

    Returns:
        Tuple of (cleaned date string, timezone string or None)
    """
    # Check for timezone abbreviations (EST, PST, etc.)
    tz_pattern = r'([A-Z]{3}T?)'
    match = re.search(tz_pattern, date_str)
    if match:
        tz_abbr = match.group(1)
        if tz_abbr in TIMEZONE_ABBR:
            # Remove the timezone abbreviation from the date string
            clean_date = re.sub(r'\s+' + tz_abbr, '', date_str)
            return clean_date, TIMEZONE_ABBR[tz_abbr]

    # Check for UTC offset patterns like +0000, -0500, etc.
    offset_pattern = r'([+-]\d{4})'
    match = re.search(offset_pattern, date_str)
    if match:
        offset = match.group(1)

        # Convert offset to timezone name (approximate)
        if offset == '+0000' or offset == '-0000':
            return date_str, 'UTC'
        elif offset == '-0500' or offset == '-0400':
            return date_str, 'US/Eastern'
        elif offset == '-0600' or offset == '-0500':
            return date_str, 'US/Central'
        elif offset == '-0700' or offset == '-0600':
            return date_str, 'US/Mountain'
        elif offset == '-0800' or offset == '-0700':
            return date_str, 'US/Pacific'

    # No timezone found
    return date_str, None


def parse_date_with_timezone(date_str: str, default_timezone: str = 'US/Pacific') -> Tuple[str, Optional[str]]:
    """
    Parse a date string and convert it to EST timezone.

    Args:
        date_str: Date string to parse
        default_timezone: Default timezone to use if none is found in the date string

    Returns:
        Tuple of (EST date in ISO format, original timezone string or None)
    """
    if not date_str:
        return "", None

    try:
        # Extract timezone information if present
        clean_date_str, tz_str = extract_timezone_from_date(date_str)

        if tz_str is None:
            # Parse the date
            parsed_date = parser.parse(clean_date_str, tzinfos=TZINFOS)
        else:
            parsed_date = parser.parse(date_str, tzinfos=TZINFOS)

        # If no timezone info was found, use the default
        if parsed_date.tzinfo is None:
            tz_str = default_timezone
            logger.debug(f"No timezone found, using default: {tz_str}")
            timezone = pytz.timezone(tz_str)
            parsed_date = timezone.localize(parsed_date)
            logger.debug(f"Added timezone {tz_str} to date: {parsed_date}")

        if tz_str != 'US/Eastern':
            # Convert to EST
            est_timezone = pytz.timezone('US/Eastern')
            parsed_date = parsed_date.astimezone(est_timezone)
            logger.debug(f"Converted date to EST: {parsed_date.isoformat()}")

        return parsed_date.isoformat(), tz_str
    except Exception as e:
        logger.error(f"Error parsing date '{date_str}': {e}")
        return "", None


def convert_timezone(dt: datetime, tz_str: str = 'America/New_York') -> datetime:
    if dt.tzinfo is None:
        # Naive datetime, assume it's in local time or UTC (your choice)
        # Here we assume UTC
        dt = dt.replace(tzinfo=ZoneInfo("UTC"))
    # Convert to Eastern Time
    return dt.astimezone(ZoneInfo(tz_str))
