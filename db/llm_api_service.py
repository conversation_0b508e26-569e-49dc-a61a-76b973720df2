from typing import Any, Dict, List, Optional
from sqlalchemy import and_, func, select
from sqlalchemy.dialects.postgresql import insert as pg_insert

from db.database_connection import DatabaseConnection
from db.models import LlmApiBatches, LlmApiResults

from db.utils import with_retries
from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='database.log')


class LlmApiService:
    def __init__(self, connection: DatabaseConnection):
        self.connection = connection

    @with_retries()
    def upsert_llm_batch(self, status: Dict[str, Any]) -> str:
        values = {
            'id': status['id'],
            'api': status.get('api'),
            'status': status.get('status'),
            'created_at': status.get('created_at'),
            'completed_at': status.get('completed_at'),
            'expires_at': status.get('expires_at'),
            'prompt_type': status.get('prompt_type'),
            'raw_response': status.get('raw_response'),
            'cost': status.get('cost')
        }

        stmt = pg_insert(LlmApiBatches).values(values)
        update_cols = {
            k: getattr(stmt.excluded, k) for k in values
            if k != "id" and values[k] is not None}

        stmt = stmt.on_conflict_do_update(
            index_elements=['id'],
            set_=update_cols
        )

        with self.connection.get_session() as session:
            session.execute(stmt)
            session.flush()

        return values['id']

    @with_retries()
    def get_llm_batch_status(
        self,
        api: Optional[str] = None,
        prompt_type: Optional[str] = None,
        included_status: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch batch status.

        Args:
            prompt_type: Prompt type of the batch.

        Returns:
            List of batch status fields.
        """
        with self.connection.get_session() as session:
            filters = []
            if api:
                filters.append(LlmApiBatches.api == api)
            if prompt_type:
                filters.append(LlmApiBatches.prompt_type == prompt_type)
            if included_status:
                filters.append(LlmApiBatches.status.in_(included_status))
            stmt = select(LlmApiBatches).where(*filters)
            results = session.execute(stmt).scalars().all()
            if not results:
                return []
            return [r.to_dict() for r in results]

    @with_retries()
    def upsert_llm_result(self, result: Dict[str, Any]) -> str:
        """
        Upsert a record into the LlmApiResults table.

        Args:
            result: Dictionary containing all result fields.

        Raises:
            SQLAlchemyError if the operation fails.
        """
        values = {
            "id": result["id"],
            'article_id': result["article_id"],
            "api": result.get("api"),
            "model": result.get("model"),
            "prompt_type": result.get("prompt_type"),
            "prompt": result.get('prompt'),
            "batch_id": result.get('batch_id'),
            "status": result.get("status"),
            "content": result.get("content"),
            "raw_response": result.get("raw_response"),
            "cost": result.get("cost")
        }

        stmt = pg_insert(LlmApiResults).values(values)

        update_fields = {
            k: getattr(stmt.excluded, k) for k in values
            if k != "id" and values[k] is not None}

        upsert_stmt = stmt.on_conflict_do_update(
            index_elements=["id"],
            set_=update_fields
        )

        with self.connection.get_session() as session:
            session.execute(upsert_stmt)
            session.flush()

        return values['id']

    @with_retries()
    def get_llm_results(
        self,
        api: Optional[str] = None,
        model: Optional[str] = None,
        prompt_type: Optional[str] = None,
        status: Optional[List[str]] = None,
        batch_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Fetch results.

        Args:
            prompt_type: Prompt type of the batch.
            status: Status of the result.
        Returns:
            List of batch status fields.
        """
        with self.connection.get_session() as session:
            filters = []
            if prompt_type is not None:
                filters.append(LlmApiResults.prompt_type == prompt_type)
            if status is not None:
                filters.append(LlmApiResults.status.in_(status))
            if api is not None:
                filters.append(LlmApiResults.api == api)
            if model is not None:
                filters.append(LlmApiResults.model == model)
            if batch_id is not None:
                filters.append(LlmApiResults.batch_id == batch_id)

            stmt = select(LlmApiResults).where(and_(*filters))
            results = session.execute(stmt).scalars().all()
            if not results:
                return []
            return [r.to_dict() for r in results]

    @with_retries()
    def get_result_by_id(self, id: str) -> Dict[str, Any] | None:
        with self.connection.get_session() as session:
            result = session.execute(
                select(LlmApiResults).where(LlmApiResults.id == id)
            ).scalars().first()
            return result.to_dict() if result else None

    @with_retries()
    def get_total_cost(self, api: Optional[str] = None) -> float:
        """
        Returns the sum of `cost` from LlmApiResults, optionally filtered by API name.

        Args:
            api (Optional[str]): Filter by this API name if provided.

        Returns:
            float: Total cost.
        """
        with self.connection.get_session() as session:
            query = session.query(func.sum(LlmApiResults.cost))
            if api is not None:
                query = query.filter(LlmApiResults.api == api)
            total = query.scalar()
            return total or 0.0
