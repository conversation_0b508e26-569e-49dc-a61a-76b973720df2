from contextlib import contextmanager

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='database.log')


DEFAULT_POOL_SIZE = 10
DEFAULT_MAX_OVERFLOW = 20


class DatabaseConnection:
    """Manages database connection and session lifecycle."""

    def __init__(self, database_url: str):
        self.engine = create_engine(
            database_url,
            pool_size=DEFAULT_POOL_SIZE,
            max_overflow=DEFAULT_MAX_OVERFLOW,
            pool_pre_ping=True,
            echo=False  # Set to True for SQL debugging
        )

        self.SessionLocal = scoped_session(
            sessionmaker(bind=self.engine, autoflush=False, autocommit=False)
        )

    @contextmanager
    def get_session(self):
        """Context manager for database sessions."""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()

    def close(self):
        """Close all database connections."""
        self.SessionLocal.remove()
        self.engine.dispose()
