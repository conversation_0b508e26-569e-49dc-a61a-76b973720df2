"""
Utility functions for the NLP module.
Provides shared functionality for text processing across different components.
"""

import logging
from typing import Dict, Any, List, Optional

from nlp.config import CHUNK_CONFIGS
from nlp.text_processing import (
    get_available_backends,
    BACKEND_SPACY,
    BACKEND_NLTK,
    BACKEND_REGEX
)

# Configure logger for this module
logger = logging.getLogger(__name__)

try:
    import tiktoken
    tokenizer = tiktoken.get_encoding("cl100k_base")
    TOKENIZER_AVAILABLE = True
except ImportError as e:
    TOKENIZER_AVAILABLE = False


def estimate_tokens(text: str) -> int:
    if not text:
        return 0
    if TOKENIZER_AVAILABLE:
        return len(tokenizer.encode(text))
    else:
        return len(text.split()) * 1.3  # rough estimate


def get_best_backend():
    """
    Get the best available text processing backend.

    Returns:
        str: The name of the best available backend
    """
    available_backends = get_available_backends()
    logger.info(f"Available NLP backends: {', '.join(available_backends)}")

    if BACKEND_SPACY in available_backends:
        backend = BACKEND_SPACY
        logger.info("Using spaCy backend for text processing")
    elif BACKEND_NLTK in available_backends:
        backend = BACKEND_NLTK
        logger.info("Using NLTK backend for text processing")
    else:
        backend = BACKEND_REGEX
        logger.info("Using regex backend for text processing")

    return backend


def split_text_into_chunks(
    text: str,
    chunk_size_words: int = CHUNK_CONFIGS['chunk_size_words'],
    min_chunk_size_words: int = CHUNK_CONFIGS['min_chunk_size_words'],
    respect_sentences: bool = CHUNK_CONFIGS['respect_sentences']
) -> List[str]:
    """
    Split text into chunks of approximately chunk_size_words words.

    Args:
        text: The text to split into chunks
        chunk_size_words: Target number of words per chunk
        min_chunk_size_words: Minimum number of words for a chunk to be analyzed
        respect_sentences: Whether to respect sentence boundaries when creating chunks

    Returns:
        List of strings with chunk text
    """
    if not text:
        return []

    chunks = []
    # If we need to respect sentence boundaries, try to split into sentences
    if respect_sentences:
        try:
            # Try to import text processing utilities
            try:
                from nlp.text_processing import split_into_sentences
                from nlp.utils import get_best_backend

                # Get the best available backend
                backend = get_best_backend()

                # Split text into sentences
                sentences = split_into_sentences(text, backend)
            except ImportError:
                # If nlp module is not available, fall back to simple regex-based sentence splitting
                import re
                sentences = re.split(r'(?<=[.!?])\s+', text)
                sentences = [s.strip() for s in sentences if s.strip()]

            # Group sentences into chunks
            current_chunk = []
            current_word_count = 0

            for sentence in sentences:
                # Count words in this sentence
                sentence_words = len(sentence.split())

                # If adding this sentence would exceed the chunk size and we already have content,
                # save the current chunk and start a new one
                if current_word_count + sentence_words > chunk_size_words and current_word_count >= min_chunk_size_words:
                    # Save the current chunk
                    chunk_text = ' '.join(current_chunk)
                    chunks.append(chunk_text)

                    # Start a new chunk with this sentence
                    current_chunk = [sentence]
                    current_word_count = sentence_words
                else:
                    # Add this sentence to the current chunk
                    current_chunk.append(sentence)
                    current_word_count += sentence_words

            # Add the last chunk if it's greater or equal to minimum size
            if current_chunk and current_word_count >= min_chunk_size_words:
                chunk_text = ' '.join(current_chunk)
                chunks.append(chunk_text)

        except Exception as e:
            logger.error(
                f"Error splitting text into sentences: {e}. Returning empty chunks.")
    else:
        # Simple chunking by words
        words = text.split()

        for i in range(0, len(words), chunk_size_words):
            end_idx = min(i + chunk_size_words, len(words))

            # Skip chunks that are too small
            if end_idx - i < min_chunk_size_words and i > 0:
                break

            # Get the chunk text
            chunk_words = words[i:end_idx]
            chunk_text = ' '.join(chunk_words)

            chunks.append(chunk_text)

    return chunks
