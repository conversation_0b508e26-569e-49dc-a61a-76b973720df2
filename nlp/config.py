"""
Configuration file for NLP module.
Contains default configurations for operators and text chunking.
"""

from pathlib import Path
from predictor.config import BASE_MODEL_NAME, CLASS_LABELS, INDICATOR_MODEL_NAME, MODEL_DIR

# Default labels for sentiment analysis
SENTIMENT_LABELS = ["positive", "negative", "neutral"]

# Default operator configurations
OPERATOR_CONFIGS = {
    "sentiment": {
        "model_name": BASE_MODEL_NAME,
        "labels": SENTIMENT_LABELS
    },
    "indicator": {
        "model_name": MODEL_DIR / INDICATOR_MODEL_NAME,
        "labels": list(CLASS_LABELS.values())
    }
}

# Default chunking configurations
CHUNK_CONFIGS = {
    "chunk_size_words": 200,
    "min_chunk_size_words": 100,
    "respect_sentences": True
}

# Default weights for combining scores from different components
SCORE_WEIGHTS = {
    "title": 0.3,
    "content": 0.7
}

# Text processing settings
# Threshold for considering sentences as similar (0.0 to 1.0)
SIMILARITY_THRESHOLD = 0.8
# Minimum length of a sentence to keep (in characters)
MIN_SENTENCE_LENGTH = 10
# Maximum number of sentences to use from each article
MAX_SENTENCES_PER_ARTICLE = 50
