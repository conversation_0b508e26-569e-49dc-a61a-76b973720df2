"""
Natural Language Processing (NLP) module for the NewsMonitor project.
Provides text processing utilities for news articles.
"""

from nlp.text_processing import (
    split_into_sentences,
    remove_stop_words,
    remove_punctuation,
    calculate_similarity,
    filter_sentences,
    process_text,
    get_available_backends,
    BACKEND_SPACY,
    BACKEND_NLTK,
    BACKEND_REGEX
)

from nlp.utils import (
    get_best_backend,
    split_text_into_chunks
)

# Import sentiment analysis
from nlp.sentiment import SentimentAnalyzer

__all__ = [
    # Text processing functions
    'split_into_sentences',
    'remove_stop_words',
    'remove_punctuation',
    'calculate_similarity',
    'filter_sentences',
    'process_text',
    'get_available_backends',
    'BACKEND_SPACY',
    'BACKEND_NLTK',
    'BACKEND_REGEX',

    # Utility functions
    'get_best_backend',
    'split_text_into_chunks',

    # Sentiment analysis
    'SentimentAnalyzer'
]
