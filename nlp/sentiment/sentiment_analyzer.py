"""
News analyzer module for financial news.
Uses transformer models to analyze sentiment and predict market movement from financial news articles.
"""

import os
from pathlib import Path
import torch
import logging
from typing import Dict, List, Union, Tuple, Optional
from transformers import AutoModelForSequenceClassification, AutoTokenizer

# Import from config
from nlp.config import OPERATOR_CONFIGS, SCORE_WEIGHTS
from nlp.utils import split_text_into_chunks
from utils.huggingface_model_loader import ModelLoader

# Configure logger
logger = logging.getLogger(__name__)


class Operator:
    """
    Operator for a specific analysis task.
    """

    def __init__(
            self,
            model_name_or_path: Union[str, Path],
            labels: List[str]
    ):
        """
        Initialize the operator.

        Args:
            model_name_or_path: Name or path of the transformer model to use.
                       If a string is provided, it will be treated as a model name from HuggingFace.
                       If a Path is provided, it will be treated as a local model path.
            labels: List of labels for the task.
        """
        self.labels = labels
        self.model, self.tokenizer = ModelLoader().load(
            model_name_or_path,
            model_type="sequence_classification",
            num_labels=len(labels)
        )

    def analyze(self, text: str) -> Dict[str, Union[str, Dict[str, float]]]:
        """
        Analyze text for this task.

        Args:
            text: Text to analyze.

        Returns:
            Dictionary with analysis results.
        """
        if not text:
            logger.warning(f"Empty text provided for analysis")
            return {
                "label": "neutral",
                "scores": {label: 0.0 for label in self.labels}
            }

        # Tokenize text
        inputs = self.tokenizer(
            text, return_tensors="pt", truncation=True, padding=True)

        # Get predictions
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
            scores = predictions[0].tolist()
            predicted_class = torch.argmax(predictions, dim=-1).item()
            predicted_label = self.labels[predicted_class]

        # Create result dictionary
        result = {
            "label": predicted_label,
            "scores": {label: score for label, score in zip(self.labels, scores)}
        }

        return result


class SentimentAnalyzer:
    """
    Sentiment analyzer for financial news using transformer models.
    """

    def __init__(self, tasks: List[str]):
        """
        Initialize the sentiment analyzer.

        Args:
            tasks: List of task names to initialize operators for.
        """
        self.operators = {}
        for task in tasks:
            if task in OPERATOR_CONFIGS:
                config = OPERATOR_CONFIGS[task]
                self.operators[task] = Operator(
                    config["model_name"], config["labels"])
                logger.info(f"Initialized operator: {task}")
            else:
                logger.warning(
                    f"Operator {task} not found in OPERATOR_CONFIGS")

    def _get_article_scores_with_chunks(
        self,
        chunks: List[Dict],
        task_name: str
    ) -> Tuple[Dict[str, Union[str, Dict[str, float]]], List[Dict]]:
        """
        Calculate final analysis scores for an article based on its chunks.

        Args:
            chunks: chunks of article
            task_name: Name of the analysis task.

        Returns:
            Dictionary with final analysis results.
        """
        # Calculate final results using weights from config
        operator = self.operators[task_name]
        combined_scores = {label: 0.0 for label in operator.labels}
        total_weight = 0.0
        chunk_result = []

        # Process each chunk based on its type
        for chunk in chunks:
            chunk_type = chunk.get('type', 'content')
            weight = SCORE_WEIGHTS.get(chunk_type, 0.0)

            chunk_analysis = operator.analyze(chunk['document'])
            chunk_result.append(chunk_analysis)

            for label in operator.labels:
                combined_scores[label] += weight * \
                    chunk_analysis['scores'][label]
            total_weight += weight

        # Normalize scores if we have any weights
        if total_weight > 0:
            for label in combined_scores:
                combined_scores[label] /= total_weight

        # Determine label from combined scores
        result = {
            "label": max(combined_scores, key=combined_scores.get),
            "scores": combined_scores
        }

        return result, chunk_result

    def analyze_text(self, text: str, tasks: List[str] = None) -> Dict[str, Dict[str, Union[str, Dict[str, float]]]]:
        """
        Analyze text using all configured operators.
        """
        tasks = tasks or self.operators.keys()
        results = {}
        for task_name in tasks:
            if task_name not in self.operators.keys():
                logger.warning(f"No operator for task {task_name}")
                continue
            results[task_name] = self.operators[task_name].analyze(text)
        return results

    def analyze_article_with_chunks(
        self,
        article: Dict[str, str],
        tasks: List[str] = None
    ) -> Dict[str, Dict[str, Union[str, Dict[str, float]]]]:
        """
        Analyze an article with chunks using all configured operators.

        Args:
            article: Article dictionary with 'title' and 'content' keys.
            tasks: List of tasks to analyze.

        Returns:
            Dictionary with analysis results for each task.
        """
        # Create chunks if they don't exist
        if 'chunks' not in article:
            chunks = []

            # Add title as a chunk if present
            if 'title' in article:
                chunks.append({
                    'document': article['title'],
                    'metadata': {
                        'type': 'title',
                        'word_count': len(article['title'].split()),
                        'chunk_idx': 0
                    },
                })

            # Add content chunks if present
            if 'content' in article:
                content_chunks = split_text_into_chunks(article['content'])
                for idx, chunk in enumerate(content_chunks):
                    chunks.append({
                        'document': chunk,
                        'metadata': {
                            'type': 'content',
                            'word_count': len(chunk.split()),
                            'chunk_idx': idx + 1
                        },
                    })
        else:
            chunks = article['chunks']

        # Analyze with each operator
        tasks = tasks or self.operators.keys()
        analysis_results = {}
        for task_name in tasks:
            if task_name not in self.operators.keys():
                logger.warning(f"No operator for task {task_name}")
                continue
            results, chunk_results = self._get_article_scores_with_chunks(
                chunks, task_name)
            analysis_results[task_name] = results
            for chunk, chunk_result in zip(chunks, chunk_results):
                if 'metadata' not in chunk:
                    chunk['metadata'] = {}
                chunk['metadata'][f"{task_name}_analysis"] = chunk_result

        return analysis_results, chunks
