import time
import random
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging
from dataclasses import dataclass

from apis.llm.base import BaseAPIManager
from apis.llm.data_types import CompletionRequest, BatchResponse, BatchStatus, CompletionStatus
from apis.llm.openai import OpenAIManager
from apis.llm.anthropic import AnthropicManager
from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.llm_news_analyzer import ArticleProcessor, _generate_custom_id

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='batch_services.log')

@dataclass
class BatchCreationConfig:
    """Configuration for batch creation service."""
    primary_api: str = 'openai'
    fallback_api: str = 'anthropic'
    max_retries: int = 3
    initial_backoff: float = 1.0
    max_backoff: float = 60.0
    jitter: float = 0.1
    batch_size: int = 20
    max_tokens: int = 512
    budget_limit: float = 1.0

class BatchCreationService:
    """Service for creating and submitting batch requests."""
    
    def __init__(self, config: BatchCreationConfig, db: DatabaseManager, prompt_manager: PromptManager):
        self.config = config
        self.db = db
        self.prompt_manager = prompt_manager
        
        # Initialize API managers
        self.api_managers = {
            'openai': OpenAIManager(requests_per_minute=10, budget_limit=config.budget_limit),
            'anthropic': AnthropicManager(requests_per_minute=10, budget_limit=config.budget_limit)
        }
        
        # Initialize article processor for each API
        self.article_processors = {
            api_name: ArticleProcessor(
                config=self._create_processor_config(api_name),
                db=db,
                llm_api=api_manager,
                prompt_manager=prompt_manager
            ) for api_name, api_manager in self.api_managers.items()
        }
    
    def _create_processor_config(self, api_name):
        """Create a config object for the article processor."""
        from nlp.llm.llm_news_analyzer import LLMConfig
        return LLMConfig(
            api_name=api_name,
            budget_limit=self.config.budget_limit,
            max_tokens=self.config.max_tokens,
            batch_size=self.config.batch_size
        )
    
    def create_batch_requests(self, prompt_type: str, model: str, articles: List[Dict[str, Any]]) -> List[CompletionRequest]:
        """Process articles and create batch requests."""
        requests = []
        prompt = self.prompt_manager.get_prompt(prompt_type)
        system_prompt = prompt['system_prompt']
        
        for article in articles:
            # Use the primary API's article processor
            article_processor = self.article_processors[self.config.primary_api]
            article_input = article_processor.process_article(article)
            if not article_input:
                continue
                
            rid = _generate_custom_id(
                article['id'], prompt_type, self.config.primary_api)
                
            formatted_prompt = prompt['prompt_template'].format(
                title=article_input.title,
                content=article_input.content
            )
            
            request = CompletionRequest(
                max_tokens=self.config.max_tokens,
                temperature=0.7,
                user_prompt=formatted_prompt,
                system_prompt=system_prompt,
                model=model,
                custom_id=rid
            )
            requests.append(request)
            
        return requests
    
    def submit_batch(self, requests: List[CompletionRequest], prompt_type: str, model: str) -> Optional[BatchResponse]:
        """Submit batch with retry and fallback logic."""
        if not requests:
            logger.warning("No valid requests to submit")
            return None
            
        # Try primary API first
        primary_api = self.config.primary_api
        fallback_api = self.config.fallback_api
        
        # Attempt with exponential backoff
        retry_count = 0
        backoff = self.config.initial_backoff
        
        while retry_count < self.config.max_retries:
            # Try primary API
            try:
                logger.info(f"Attempting batch submission with {primary_api}, retry {retry_count+1}")
                batch = self.api_managers[primary_api].get_completion_batch(requests)
                if batch:
                    self._save_batch_and_requests(batch, requests, prompt_type, primary_api)
                    return batch
            except Exception as e:
                logger.error(f"Primary API ({primary_api}) batch submission failed: {e}")
            
            # Try fallback API
            try:
                logger.info(f"Attempting fallback batch submission with {fallback_api}")
                # Need to regenerate custom IDs for the fallback API
                fallback_requests = self._regenerate_custom_ids(requests, prompt_type, fallback_api)
                batch = self.api_managers[fallback_api].get_completion_batch(fallback_requests)
                if batch:
                    self._save_batch_and_requests(batch, fallback_requests, prompt_type, fallback_api)
                    return batch
            except Exception as e:
                logger.error(f"Fallback API ({fallback_api}) batch submission failed: {e}")
            
            # Apply exponential backoff with jitter
            jitter_factor = 1 + random.uniform(-self.config.jitter, self.config.jitter)
            sleep_time = min(backoff * jitter_factor, self.config.max_backoff)
            logger.info(f"Backing off for {sleep_time:.2f} seconds before retry")
            time.sleep(sleep_time)
            
            # Increase backoff for next attempt
            backoff *= 2
            retry_count += 1
        
        logger.error(f"Failed to submit batch after {self.config.max_retries} retries")
        return None
    
    def _regenerate_custom_ids(self, requests: List[CompletionRequest], prompt_type: str, api_name: str) -> List[CompletionRequest]:
        """Regenerate custom IDs for a different API."""
        new_requests = []
        for request in requests:
            # Extract article ID from the original custom ID
            article_id = request.custom_id.split("_")[1]
            new_custom_id = _generate_custom_id(article_id, prompt_type, api_name)
            
            # Create a new request with the updated custom ID
            new_request = CompletionRequest(
                max_tokens=request.max_tokens,
                temperature=request.temperature,
                user_prompt=request.user_prompt,
                system_prompt=request.system_prompt,
                model=request.model,
                custom_id=new_custom_id
            )
            new_requests.append(new_request)
        
        return new_requests
    
    def _save_batch_and_requests(self, batch: BatchResponse, requests: List[CompletionRequest], 
                                prompt_type: str, api_name: str) -> None:
        """Save batch and mark requests as in progress."""
        # Save batch to database
        batch_dict = batch.to_dict()
        batch_dict['api'] = api_name
        batch_dict['prompt_type'] = prompt_type
        
        try:
            self.db.llm_api_service.upsert_llm_batch(batch_dict)
            logger.info(f"Saved batch {batch.id} to database")
            
            # Mark all requests as in progress
            for request in requests:
                request_dict = request.to_dict()
                request_dict['status'] = CompletionStatus.IN_PROGRESS.value
                request_dict['batch_id'] = batch.id
                
                # Extract article ID from custom ID
                article_id = request.custom_id.split("_")[1]
                request_dict['article_id'] = article_id
                
                self.db.llm_api_service.upsert_llm_result(request_dict)
            
            logger.info(f"Marked {len(requests)} requests as in progress")
        except Exception as e:
            logger.error(f"Error saving batch or requests: {e}")