#!/usr/bin/env python
"""
Batch processor script for LLM News Analyzer.

This script runs the batch processing service independently to poll for and process
active batches at regular intervals.
"""

import argparse
import time
import logging
import signal
import sys
from typing import Optional

from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.batch_services import BatchCreationConfig, BatchProcessingService
from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='batch_processor.log')

# Global flag for graceful shutdown
running = True

def signal