import json
import argparse
import time
from typing import List, Set, Optional, Tuple, Union, Dict, Any
from zoneinfo import ZoneInfo
from dataclasses import dataclass
from datetime import date, datetime, timedelta

import pandas as pd
from tqdm import tqdm

from apis.llm.base import BaseAPIManager
from apis.llm.data_types import (
    ACTIVE_BATCH_STATUSES,
    COMPLETED_BATCH_STATUSES,
    INCOMPLETED_BATCH_STATUSES,
    BatchResponse,
    CompletionRequest,
    CompletionResponse,
    CompletionStatus,
    BatchStatus
)
from nlp.llm.prompt import PromptManager, PROMPT_SHORT_NAME
from apis.llm.anthropic import AnthropicManager
from apis.llm.openai import OpenAIManager
from apis.yahoo_finance import yahoo_api
from db.database import DatabaseManager
from nlp.llm.batch_services import BatchCreationService, BatchProcessingService, BatchCreationConfig

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='llm_analyzer.log')

# Constants
API_SHORT_NAMES = {
    'openai': 'OAI',
    'anthropic': 'ANC'
}

MAX_CUSTOM_ID_CHARS = 64
TZ_NAME = "America/New_York"
TZ = ZoneInfo("America/New_York")


@dataclass
class LLMConfig:
    """Configuration for analysis settings."""
    api_name: str = 'openai'
    budget_limit: float = 1.0
    max_tokens: int = 512
    max_input: int = 1024
    min_input: int = 200
    batch_size: int = 20

    def validate(self) -> None:
        """Validate configuration parameters."""
        if self.budget_limit <= 0:
            raise ValueError("Budget limit must be positive")
        if self.max_tokens <= 0:
            raise ValueError("Max tokens must be positive")
        if self.max_input <= self.min_input:
            raise ValueError("Max input must be greater than min input")
        if self.api_name not in API_SHORT_NAMES:
            raise ValueError(f"Unsupported API: {self.api_name}")


@dataclass
class ArticleInput:
    """Structured article input."""
    title: str
    content: str

    @property
    def word_count(self) -> int:
        return len(self.title.split()) + len(self.content.split())


class ArticleProcessor:
    """Handles article processing and validation."""

    def __init__(self, config: LLMConfig, db: DatabaseManager, llm_api: BaseAPIManager, prompt_manager: PromptManager):
        self.config = config
        self.db = db
        self.llm_api = llm_api
        self.prompt_manager = prompt_manager

    def process_article(self, article: Dict[str, Any]) -> Optional[ArticleInput]:
        """Process and validate article input."""
        title_words = article.get('title', '').split()
        content_words = article.get('content', '').split()

        total_words = len(title_words) + len(content_words)

        if total_words < self.config.min_input:
            logger.debug(
                f"Skipped short article {article.get('url')} of {total_words} words.")
            return None

        if total_words > self.config.max_input:
            logger.warning(
                f"Trimmed long article from {total_words} to {self.config.max_input} words")

            if len(title_words) > self.config.max_input:
                title_words = title_words[:self.config.max_input]
                content_words = []
            else:
                content_words = content_words[:self.config.max_input -
                                              len(title_words)]

        return ArticleInput(
            title=' '.join(title_words),
            content=' '.join(content_words)
        )

    def save_completion(self, result_dict: Dict[str, Any], prompt_type: str, batch_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Save completion result to database."""
        try:
            article_id = _extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": self.config.api_name,
                "prompt_type": prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "batch_id": batch_id,
                "cost": result_dict.get('cost') or result_dict.get('estimated_cost'),
                "raw_response": result_dict.get('raw_response')
            }
            if 'content' in result_dict:
                db_record['content'] = self.prompt_manager.parse_result(
                    result_dict['content'], prompt_type)

            if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
                db_record['prompt'] = result_dict['user_prompt'] + \
                    result_dict['system_prompt']

            result_id = self.db.llm_api_service.upsert_llm_result(db_record)
            if result_id:
                logger.info(f"Successfully saved result: {result_id}")

                # Save result to article metadata
                if db_record['content'] and self.db.article_service.add_article_metadata(
                        article_id, {prompt_type: db_record['content']}):
                    logger.info(
                        f"Successfully saved in article metadata: {article_id} ")

                return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return None

    def analyze_article(self, article: Dict[str, Any], prompt_type: str, model: str) -> Dict[str, Any]:
        """Analyze a single article using the specified prompt type."""
        try:
            # Process article input
            article_input = self.process_article(article)
            if not article_input:
                return {}

            # Get and format prompt
            prompt = self.prompt_manager.get_prompt(prompt_type)
            system_prompt = prompt['system_prompt']
            formatted_prompt = prompt['prompt_template'].format(
                title=article_input.title,
                content=article_input.content
            )

            request = CompletionRequest(
                max_tokens=self.config.max_tokens,
                temperature=0.7,
                user_prompt=formatted_prompt,
                system_prompt=system_prompt,
                model=model
            )

            # Call LLM API
            completion = self.llm_api.get_completion(request)
            if completion:
                completion.custom_id = _generate_custom_id(
                    article['id'], prompt_type, self.config.api_name)
                result = self.save_completion(
                    completion.to_dict(), prompt_type)
                if result and result['content']:
                    return {prompt_type: result['content']}
        except Exception as e:
            logger.error(
                f"Error analyzing article {article.get('id', 'unknown')}: {e}")
        return {}


def _extract_article_id(custom_id: str) -> str:
    """Extract article ID from custom ID."""
    return custom_id.split("_")[1]


def _generate_custom_id(article_id: str, prompt_type: str, api_name: str) -> str:
    """Generate a custom ID for the analysis request."""
    prompt_sn = PROMPT_SHORT_NAME[prompt_type]
    api_sn = API_SHORT_NAMES[api_name]
    custom_id = f"{api_sn}_{article_id}_{prompt_sn}"

    if len(custom_id) > MAX_CUSTOM_ID_CHARS:
        raise ValueError(
            f"Custom ID too long: {len(custom_id)} > {MAX_CUSTOM_ID_CHARS}")

    return custom_id


def get_target_dates(
    start_date: Union[str, datetime],
    end_date: Union[str, datetime],
    threshold: float = 0.01,
    interval: str = "1d",
    days_before: int = 3,
    days_after: int = 3,
) -> Set[date]:
    """
    Fetch dates where SPY's absolute daily return exceeded the given threshold.

    Args:
        start_date: Start date for analysis.
        end_date: End date for analysis.
        threshold: Minimum absolute daily return (e.g., 0.01 for 1%).
        interval: Data interval, default is "1d".
        days_before: How many days before the target date to include.
        days_after: How many days after the target date to include.

    Returns:
        Set of target dates (datetime.date).
    """
    # Parse dates if they are strings
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date).tz_localize(TZ_NAME)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date).tz_localize(TZ_NAME)

    try:
        df = yahoo_api.get_price_data(
            ticker="SPY",
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
    except Exception as e:
        logger.error(f"Error fetching price data: {e}")
        return set()

    if df.empty or "Close" not in df.columns:
        logger.warning("No price data available for the specified period")
        return set()

    df["return"] = df["Close"].pct_change()
    mask = df["return"].abs() > threshold
    filtered = df[mask]

    result = set()
    for ts in filtered.index:
        base_date = ts.replace(tzinfo=TZ).date()
        for offset in range(-days_before, days_after + 1):
            result.add(base_date + timedelta(days=offset))

    return result


class BatchProcessor:
    """Handles batch processing of articles."""

    def __init__(self, config: LLMConfig, llm_api: BaseAPIManager, db: DatabaseManager,
                 prompt_manager: PromptManager, article_processor: ArticleProcessor):
        self.config = config
        self.llm_api = llm_api
        self.db = db
        self.prompt_manager = prompt_manager
        self.article_processor = article_processor

    def save_batch(self, batch_dict: Dict[str, Any], prompt_type: str) -> Dict[str, Any]:
        """Save batch request information to database."""
        try:
            db_record = {
                'id': batch_dict['id'],
                'api': self.config.api_name,
                'status': batch_dict['status'],
                'created_at': batch_dict['created_at'],
                'completed_at': batch_dict.get('completed_at'),
                'expires_at': batch_dict['expires_at'],
                'prompt_type': prompt_type,
                'raw_response': batch_dict['raw_response'],
                'cost': batch_dict.get('cost')
            }
            self.db.llm_api_service.upsert_llm_batch(db_record)

            logger.info(f"Successfully saved batch: {batch_dict['id']}")
            return db_record

        except Exception as e:
            logger.error(f"Error saving batch: {e}")
            return {}

    def create_batch_requests(self, prompt_type: str, model: str, target_articles: List[Dict[str, Any]]) -> List[CompletionRequest]:
        """Process articles in batch."""
        try:
            requests = []
            prompt = self.prompt_manager.get_prompt(prompt_type)
            system_prompt = prompt['system_prompt']

            for article in target_articles:
                article_input = self.article_processor.process_article(article)
                if not article_input:
                    continue

                rid = _generate_custom_id(
                    article['id'], prompt_type, self.config.api_name)

                formatted_prompt = prompt['prompt_template'].format(
                    title=article_input.title,
                    content=article_input.content
                )

                request = CompletionRequest(
                    max_tokens=self.config.max_tokens,
                    temperature=0.7,
                    user_prompt=formatted_prompt,
                    system_prompt=system_prompt,
                    model=model,
                    custom_id=rid
                )
                requests.append(request)

            return requests
        except Exception as e:
            logger.error(f"Error processing batch: {e}")
            return []


class LLMNewsAnalyzer:
    """
    Main class for analyzing news articles using LLM APIs.

    Provides both single article analysis and batch processing capabilities
    with proper error handling, caching, and structured output storage.
    """

    def __init__(self, config: LLMConfig):
        """Initialize the news analyzer with configuration."""
        self.config = config
        self.config.validate()
        self.db = DatabaseManager()
        self.prompt_manager = PromptManager()
        # Initialize LLM API
        self.llm_api = self._initialize_llm_api()
        # Initialize article processor
        self.article_processor = ArticleProcessor(
            config, self.db, self.llm_api, self.prompt_manager)

        # Initialize batch services
        batch_config = BatchCreationConfig(
            primary_api=self.config.api_name,
            fallback_api='anthropic' if self.config.api_name == 'openai' else 'openai',
            batch_size=self.config.batch_size,
            max_tokens=self.config.max_tokens,
            budget_limit=self.config.budget_limit
        )
        self.batch_creation_service = BatchCreationService(
            batch_config, self.db, self.prompt_manager
        )
        self.batch_processing_service = BatchProcessingService(
            batch_config, self.db, self.prompt_manager
        )

        self._log_initialization()

    def _initialize_llm_api(self) -> BaseAPIManager:
        """Initialize the appropriate LLM API based on configuration."""
        current_total_cost = self._get_current_total_cost()

        if current_total_cost >= self.config.budget_limit:
            logger.warning(
                f"Budget exceeded. Budget: {self.config.budget_limit}, "
                f"Cost: {current_total_cost}. Requests may fail."
            )

        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        BaseAPIManager.set_budget(self.config.budget_limit, current_total_cost)

        api_class = api_mapping.get(self.config.api_name)
        if not api_class:
            raise ValueError(f"Unsupported API: {self.config.api_name}")

        return api_class()

    def _get_current_total_cost(self) -> float:
        """Get current total cost for the API."""
        return self.db.llm_api_service.get_total_cost(self.config.api_name)

    def _log_initialization(self) -> None:
        """Log initialization information."""
        current_cost = self._get_current_total_cost()
        logger.info(
            f"Initialized LLM analyzer with {self.config.api_name} API")
        logger.info(
            f"Budget: ${self.config.budget_limit:.4f}, Spent: ${current_cost:.4f}")

    def analyze_article_by_url(self, url: str, prompt_type: str, model: str) -> Dict[str, Any]:
        """Analyze a specific article by URL."""
        try:
            article = self.db.article_service.get_article_by_url(url)
            if not article:
                logger.warning(f"Article not found: {url}")
                return {}

            rid = _generate_custom_id(
                article['id'], prompt_type, self.config.api_name)

            # Check if already analyzed
            existing_result = self.db.llm_api_service.get_result_by_id(rid)
            if existing_result:
                logger.info(f"Returning existing result for {url}")
                return {prompt_type: existing_result['content']}

            return self.article_processor.analyze_article(article, prompt_type, model)

        except Exception as e:
            logger.error(f"Error analyzing article by URL {url}: {e}")

        return {}

    def get_article_candidates(
        self,
        prompt_type: str,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        max_articles: Optional[int] = None,
        filter_target_dates: bool = False
    ) -> List[Dict[str, Any]]:
        # Get date range
        start_dt, end_dt = self.db.article_service.get_article_date_range()
        if start_date:
            start_dt = max(start_dt, datetime.strptime(
                start_date, "%Y-%m-%d").replace(tzinfo=TZ).date())
        if end_date:
            end_dt = min(end_dt, datetime.strptime(
                end_date, "%Y-%m-%d").replace(tzinfo=TZ).date())

        # Get target dates
        target_dates = get_target_dates(
            start_dt, end_dt) if filter_target_dates else None

        if target_dates:
            logger.info(f"Filter articles on {len(target_dates)} target dates")

        articles = self.db.article_service.get_articles_for_llm_batch(
            # Exclude already succeeded articles with the api and prompt type
            prompt_type,
            self.config.api_name,
            llm_excluded_statuses=[CompletionStatus.SUCCEEDED.value,
                                   CompletionStatus.IN_PROGRESS.value],
            date_list=target_dates,
            start_date=start_dt,
            end_date=end_dt,
            limit=max_articles,
            # Filter short articles
            min_words=self.config.min_input
        )
        if start_dt and end_dt:
            logger.info(
                f"Fetched {len(articles)} articles between {start_dt.strftime('%Y-%m-%d')} and {end_dt.strftime('%Y-%m-%d')}")
        else:
            logger.info(f"Fetched {len(articles)} articles")

        return articles

    def retrieve_and_process_batches(self, prompt_type: str) -> None:
        """Retrieve and process batches."""
        processed_count = self.batch_processing_service.retrieve_and_process_active_batches(
            prompt_type=prompt_type,
            api_name=self.config.api_name
        )
        logger.info(f"Processed {processed_count} batches for prompt type {prompt_type}")

    def analyze_articles_by_batch(
        self,
        prompt_type: str,
        model: str,
        max_articles: Optional[int] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        filter_target_dates: bool = False
    ):
        """Analyze articles using batch processing."""
        # Get articles to process
        articles = self.get_article_candidates(
            prompt_type, start_date, end_date, max_articles, filter_target_dates
        )

        logger.info(f"Found {len(articles)} articles to process")

        article_batches = []
        for i in range(0, len(articles), self.config.batch_size):
            article_batches.append(articles[i:i + self.config.batch_size])

        for article_batch in tqdm(article_batches, desc="Processing batches"):
            # Create batch requests
            requests = self.batch_creation_service.create_batch_requests(
                prompt_type, model, article_batch
            )
            
            # Submit batch with retry and fallback logic
            batch = self.batch_creation_service.submit_batch(requests, prompt_type, model)
            
            if not batch:
                logger.error("Failed to create batch after all retries")
                continue
                
            # Wait for batch to be validated (openai)
            if batch.status == BatchStatus.VALIDATING.value:
                logger.info(f"Batch {batch.id} is validating. Waiting for completion...")
                while batch and batch.status == BatchStatus.VALIDATING.value:
                    time.sleep(10)
                    batch = self.llm_api.retrieve_batch(batch.id, fetch_results=False)
                    if batch:
                        # Update batch record
                        batch_dict = batch.to_dict()
                        batch_dict['api'] = self.config.api_name
                        batch_dict['prompt_type'] = prompt_type
                        self.db.llm_api_service.upsert_llm_batch(batch_dict)


def create_analyzer_from_args(args) -> LLMNewsAnalyzer:
    """Create analyzer instance from command line arguments."""
    config = LLMConfig(
        api_name=args.api,
        budget_limit=args.budget,
        max_tokens=args.max_tokens,
        max_input=args.max_input,
        min_input=args.min_input,
        batch_size=args.batch_size
    )

    return LLMNewsAnalyzer(config)


def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    # Basic options
    parser.add_argument('-u', '--url', help='URL of article to analyze')
    parser.add_argument('-a', '--api', default='openai', help='LLM API name')
    parser.add_argument(
        '-m', '--model', default='gpt-4.1-nano', help='LLM model to use')
    parser.add_argument('-t', '--prompt-type',
                        default='influence', help='Type of analysis to perform')

    # Date filtering
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--filter-target-dates', action='store_true',
                        help='Filter dates based on SPY price movements')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=0.1,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=2048,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=512,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=10,
                        help='Rate limit per minute')

    # Batch processing
    parser.add_argument('--batch-mode', action='store_true',
                        help='Use batch processing')
    parser.add_argument('--max-articles', type=int, default=10_000,
                        help='Maximum number of articles')
    parser.add_argument('--batch-size', type=int, default=20,
                        help='Batch size')
    args = parser.parse_args()

    try:
        analyzer = create_analyzer_from_args(args)

        if args.batch_mode:
            analyzer.retrieve_and_process_batches(args.prompt_type)
            analyzer.analyze_articles_by_batch(
                prompt_type=args.prompt_type,
                model=args.model,
                max_articles=args.max_articles,
                start_date=args.start_date,
                end_date=args.end_date,
                filter_target_dates=args.filter_target_dates,
            )
        elif args.url:
            result = analyzer.analyze_article_by_url(
                args.url, args.prompt_type, args.model)
            if result:
                print(json.dumps(result, indent=2, default=str))
            else:
                print(
                    f"No article found or analysis failed for URL: {args.url}")
        else:
            print(
                "Please specify either --url for single article or --batch-mode for batch processing")

    except Exception as e:
        logger.error(f"Application error: {e}")
        raise


if __name__ == '__main__':
    main()
