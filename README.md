# NewsMonitor

A comprehensive financial news monitoring and SP500 price prediction system.

This project consists of three main components:

1. **Financial News Crawler**: Collects financial news articles from various sources
2. **Web Interface**: Displays SP500 index graphs and financial news
3. **SP500 Price Predictor**: Uses LLM to predict future SP500 prices based on news data

## Project Structure

```
NewsMonitor/
├── news_crawler/        # Financial news crawler
│   ├── news_crawler/    # Crawler source code
│   │   ├── spiders/     # Spider implementations
│   │   ├── items.py     # Item definitions
│   │   ├── pipelines.py # Processing pipelines
│   │   └── settings.py  # Crawler settings
│   ├── output/          # Crawler output
│   ├── run_spiders.py   # Script to run all spiders
│   └── web_server.py    # Simple web server
├── web/                 # Web interface
│   ├── data/            # Data processing modules
│   ├── static/          # Static assets
│   ├── templates/       # HTML templates
│   └── app.py           # Flask application
|── predictor/           # SP500 price predictor
|   ├── data/            # Data loading and processing
|   ├── models/          # Model implementation
|   ├── utils/           # Utility functions
|   ├── train.py         # Training script
|   └── predict.py       # Prediction script
└── utils/           
    ├── logging_config.py            
    └── path_util.py       

```

## Installation

### Option 1: Install as a Package (Recommended)

Install the entire NewsMonitor package with all components:

```
conda create -n web_crawler python=3.9
conda activate web_crawler
pip install -e .
```

This will install all required dependencies and make the command-line tools available.

### Option 2: Manual Installation

If you prefer to install components separately:

1. Create and activate a conda environment:
```
conda create -n web_crawler python=3.9
conda activate web_crawler
```

2. Install the required packages for the news crawler:
```
conda install -c conda-forge scrapy
```

3. Install the required packages for the web interface:
```
pip install -r web/requirements.txt
```

4. Install the required packages for the predictor:
```
pip install -r predictor/requirements.txt
```

## Usage

### Using the Command-Line Tools

After installing the package, you can use the following command-line tools:

#### Running the News Crawler

To collect financial news articles:

```
conda activate web_crawler
run-crawler
```

#### Running the Web Interface

To view the collected news and SP500 data:

```
conda activate web_crawler
run-web
```

Then open your browser and navigate to `http://localhost:5000`.

#### Running the Price Predictor

To train or make predictions with the SP500 price predictor:

```
conda activate web_crawler
run-predictor train  # For training
run-predictor predict  # For prediction
```

### Alternative: Running Python Scripts Directly

If you prefer to run the Python scripts directly:

```
conda activate web_crawler
python news_crawler/run_spiders.py  # Run the crawler
python web/app.py  # Run the web interface
python predictor/run_predictor.py train  # Train the predictor
```

### Training the Price Predictor

#### LLM-based Model (Text-only)

To train the text-only SP500 price predictor model:

```
conda activate web_crawler
python run_predictor.py train --feature_days=7 --max_articles=20
```

Optional arguments:
- `--end_date`: End date for training data (YYYY-MM-DD)
- `--lookback_days`: Number of days to look back for training data
- `--feature_days`: Number of past days of news to use as features
- `--max_articles`: Maximum number of articles to use
- `--model_name`: Base model to use for fine-tuning
- `--output_dir`: Directory to save the model

#### Multimodal Model (Text + Price)

To train the multimodal SP500 price predictor model:

```
conda activate web_crawler
python run_predictor.py train-multimodal --feature_days=7 --price_days=5
```

Additional arguments:
- `--price_days`: Number of past days of price data to use as features

### Making Price Predictions

#### LLM-based Model

To predict future SP500 prices using the text-only model:

```
conda activate web_crawler
python run_predictor.py predict --model_path=path/to/model
```

Optional arguments:
- `--model_path`: Path to the trained model
- `--prediction_date`: Date to predict for (YYYY-MM-DD)
- `--output_file`: Path to save prediction results

#### Multimodal Model

To predict future SP500 prices using the multimodal model:

```
conda activate web_crawler
python run_predictor.py predict-multimodal --model_path=path/to/model --feature_days=7 --price_days=5
```

Additional arguments:
- `--feature_days`: Number of past days of news to use as features
- `--price_days`: Number of past days of price data to use as features

## SP500 Price Predictor

The SP500 Price Predictor module offers two types of models to predict future SP500 price movements:

1. **LLM-based Model**: Analyzes financial news using a fine-tuned Language Model
2. **Multimodal Transformer Model**: Combines news data and historical price data for improved predictions

### Features

- Loads financial news data from the crawler output
- Preprocesses news text and price data for model input
- Supports configurable number of past days for features
- Fine-tunes models for price prediction
- Evaluates model performance using financial metrics
- Generates predictions with confidence scores

### Model Types

#### LLM-based Model (Text-only)

- Base model: BERT (bert-base-uncased)
- Input: Concatenated financial news articles from the past X days (configurable)
- Output: Predicted SP500 price change percentage
- Training: `python predictor/train.py --feature_days=7 --max_articles=20`
- Prediction: `python predictor/predict.py --model_path=path/to/model`

#### Multimodal Transformer Model (Text + Price)

- Architecture: BERT for text encoding + transformer layers for combined features
- Input:
  - News articles from the past X days (each day as a separate feature)
  - Price data from the past Y days (OHLCV and derived features)
- Processing:
  - BERT encodes each day's news articles into embeddings
  - Price features are projected to the same dimension
  - Transformer layers with position embeddings process the combined features
- Output: Predicted SP500 price change percentage
- Training: `python predictor/train_multimodal.py --feature_days=7 --price_days=5`
- Prediction: `python predictor/predict_multimodal.py --model_path=path/to/model`

### Using Different Base Models

The predictor supports different pre-trained models for the text encoding component:

1. **BERT (default)**
   - Well-balanced model for financial text analysis
   - Handles sequences up to 512 tokens
   - Good performance with reasonable resource requirements

2. **DeepSeek Coder**
   - More powerful model for complex financial analysis
   - Handles longer sequences (up to 1024 tokens)
   - Requires more computational resources
   - Change in config.py: `BASE_MODEL_NAME = "deepseek-ai/deepseek-coder-1.3b-base"`

3. **Other models**
   - Any HuggingFace Transformers model can be used
   - Update `BASE_MODEL_NAME` in config.py
   - Adjust `MODEL_MAX_LENGTH` and training parameters accordingly
