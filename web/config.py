"""
Configuration settings for the web interface.

This module provides configuration settings for the web interface,
including paths, URLs, and other settings.
"""

import os
from pathlib import Path

# Project paths
ROOT_DIR = Path(__file__).parent.resolve()
TEMPLATES_DIR = ROOT_DIR / "templates"
STATIC_DIR = ROOT_DIR / "static"
LOG_DIR = ROOT_DIR / "logs"

# Ensure directories exist
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

# Web server settings
HOST = "0.0.0.0"
PORT = 5000
DEBUG = False

# Crawler settings
CRAWLER_RUN_INTERVAL_HOURS = 3  # Run crawler every 3 hours
CRAWLER_DISABLED_BY_DEFAULT = False

# Data settings
DEFAULT_LOOKBACK_DAYS = 30  # Default number of days to look back for data
SP500_TICKER = "SPY"  # S&P 500 index ticker symbol
DEFAULT_ARTICLE_LIMIT = 20  # Default number of articles to return

# Date format
DATE_FORMAT = "%Y-%m-%d"  # YYYY-MM-DD
DISPLAY_DATE_FORMAT = "%b %d, %Y"  # e.g., Jan 01, 2023
