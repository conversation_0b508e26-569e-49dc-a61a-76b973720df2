"""
Error handling utilities for the web interface.

This module provides standardized error handling for the web interface.
"""

import traceback
from datetime import datetime
from flask import jsonify

def handle_error(e, logger=None):
    """
    Handle an exception and return a standardized error response.

    Args:
        e (Exception): The exception to handle
        logger (logging.Logger): Optional logger to log the error

    Returns:
        tuple: (JSON response, status code)
    """
    # Get the error traceback
    error_traceback = traceback.format_exc()
    
    # Log the error if a logger is provided
    if logger:
        logger.error(f"Error: {e}")
        logger.error(f"Traceback: {error_traceback}")
    
    # Create a standardized error response
    response = {
        'error': str(e),
        'traceback': error_traceback,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify(response), 500

def api_error(message, status_code=400, logger=None):
    """
    Create a standardized API error response.

    Args:
        message (str): Error message
        status_code (int): HTTP status code
        logger (logging.Logger): Optional logger to log the error

    Returns:
        tuple: (JSON response, status code)
    """
    # Log the error if a logger is provided
    if logger:
        logger.error(f"API Error ({status_code}): {message}")
    
    # Create a standardized error response
    response = {
        'error': message,
        'status_code': status_code,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify(response), status_code
