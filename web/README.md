# Market Monitor Web Server

This web server displays the S&P 500 index graph by date and allows users to click on dates to view corresponding financial news.

## Features

- Interactive S&P 500 index graph
- Date selection to view financial news
- Multiple time period views (1M, 3M, 6M, 1Y, YTD)
- News articles grouped by source
- Responsive design

## Requirements

- Python 3.8+
- Flask
- yfinance
- pandas
- plotly
- python-dateutil
- requests

## Installation

1. Navigate to the web directory:
   ```
   cd /path/to/NewsMonitor/web
   ```

2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

## Running the Web Server

1. Make sure your conda environment is activated:
   ```
   conda activate llm
   ```

2. Start the Flask server:
   ```
   python app.py
   ```

3. Open your web browser and navigate to:
   ```
   http://localhost:5000
   ```

## Usage

- The main page displays the S&P 500 index graph
- Use the time period buttons (1M, 3M, 6M, 1Y, YTD) to adjust the graph view
- Red dots on the graph indicate dates with available news
- Click on a red dot to view news articles from that date
- News articles are displayed below the graph, grouped by source

## Data Sources

- S&P 500 data is fetched from Yahoo Finance
- News data is read from database
