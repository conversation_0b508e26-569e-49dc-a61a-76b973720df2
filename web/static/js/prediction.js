// Prediction Display Functionality

document.addEventListener('DOMContentLoaded', function () {
    // DOM elements for both the old and new prediction sections
    const predictionContainer = document.getElementById('prediction-container');
    const predictionMain = document.getElementById('prediction-main');
    const predictionConfidence = document.getElementById('prediction-confidence');
    const predictionArticles = document.getElementById('prediction-articles');

    // Check if we have the new prediction section elements
    const hasNewPredictionSection = predictionMain && predictionConfidence && predictionArticles;

    // Log the elements to help with debugging
    console.log('Prediction elements found:', {
        predictionContainer: !!predictionContainer,
        predictionMain: !!predictionMain,
        predictionConfidence: !!predictionConfidence,
        predictionArticles: !!predictionArticles,
        hasNewPredictionSection: hasNewPredictionSection
    });

    // Variables
    let currentPriceValue = null;
    let currentPredictionData = null;

    // Initialize the prediction section
    console.log('Initializing prediction with current price');
    loadCurrentPrice();

    // Add event listeners for the new prediction section buttons
    const runPredictionBtnMain = document.getElementById('run-prediction-btn-main');
    if (runPredictionBtnMain) {
        runPredictionBtnMain.addEventListener('click', function () {
            showLoading();
            loadPredictionData(currentPriceValue);
        });
    }

    const viewHistoryBtnMain = document.getElementById('view-history-btn-main');
    if (viewHistoryBtnMain) {
        viewHistoryBtnMain.addEventListener('click', function () {
            showHistoryModal();
        });
    }

    const viewAllArticlesBtn = document.getElementById('view-all-articles-btn');
    if (viewAllArticlesBtn) {
        viewAllArticlesBtn.addEventListener('click', function () {
            if (currentPredictionData && currentPredictionData.relevant_articles) {
                showArticlesModal(currentPredictionData.relevant_articles);
            }
        });
    }

    // Function to show loading state
    function showLoading() {
        // Show loading state in the new prediction section if it exists
        if (hasNewPredictionSection) {
            console.log('Showing loading state in new prediction section');
            predictionMain.innerHTML = `
                <div class="placeholder-glow">
                    <span class="placeholder col-8"></span>
                </div>
            `;

            predictionConfidence.innerHTML = `
                <div class="placeholder-glow">
                    <span class="placeholder col-12"></span>
                    <span class="placeholder col-12 mt-2"></span>
                    <span class="placeholder col-12 mt-2"></span>
                </div>
            `;

            predictionArticles.innerHTML = `
                <div class="placeholder-glow">
                    <span class="placeholder col-12" style="height: 100px;"></span>
                </div>
            `;
        }
        // Show loading state in the old prediction container only if new section doesn't exist
        else if (predictionContainer) {
            console.log('Showing loading state in old prediction container (fallback)');
            predictionContainer.innerHTML = `
                <div class="card h-100 border-0">
                    <div class="card-body p-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-baseline justify-content-between">
                                <div class="d-flex align-items-baseline">
                                    <div class="placeholder-glow me-2">
                                        <span class="placeholder col-8" style="height: 2rem;"></span>
                                    </div>
                                    <div class="placeholder-glow">
                                        <span class="placeholder col-8"></span>
                                    </div>
                                </div>
                                <div class="placeholder-glow">
                                    <span class="placeholder col-4"></span>
                                </div>
                            </div>
                            <div class="placeholder-glow mt-1">
                                <span class="placeholder col-6" style="height: 0.75rem;"></span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // Function to load current price first, then prediction
    function loadCurrentPrice() {
        console.log('Loading current price...');
        showLoading();

        // Fetch current price
        fetch('/api/sp500')
            .then(response => {
                console.log('Current price response status:', response.status);
                return response.json();
            })
            .then(priceData => {
                console.log('Received price data:', priceData);

                // Extract the latest price
                if (priceData && priceData.close && priceData.close.length > 0) {
                    const latestPrice = priceData.close[priceData.close.length - 1];
                    console.log('Latest price:', latestPrice);
                    currentPriceValue = latestPrice;

                    // Now load the prediction data
                    loadPredictionData(latestPrice);
                } else {
                    console.error('Invalid price data format:', priceData);
                    // Try to load prediction data anyway
                    loadPredictionData(null);
                }
            })
            .catch(error => {
                console.error('Error fetching current price:', error);
                // If we can't get the current price, still try to load prediction
                loadPredictionData(null);
            });
    }

    // Function to load prediction data
    async function loadPredictionData(currentPrice) {
        console.log('Loading prediction data with current price:', currentPrice);
        try {
            const response = await fetch('/api/prediction');
            console.log('Prediction response status:', response.status);

            const data = await response.json();
            console.log('Received prediction data:', data);

            if (data.error) {
                throw new Error(data.error);
            }

            // Add the current price to the prediction data if it's missing
            if (currentPrice && (!data.current_price || data.current_price === 0)) {
                console.log('Adding current price to prediction data:', currentPrice);
                data.current_price = currentPrice;
            }

            // Ensure probabilities object exists
            if (!data.probabilities) {
                console.log('No probabilities found in prediction data, initializing empty probabilities object');
                data.probabilities = {
                    positive: 0.33,
                    negative: 0.33,
                    neutral: 0.34
                };
            }

            // Store the prediction data for later use
            currentPredictionData = data;

            // Render the prediction
            console.log('Rendering prediction with data:', data);
            renderPrediction(data);
        } catch (error) {
            console.error('Error fetching prediction:', error);
            showErrorMessage(error.message, currentPrice);
        }
    }

    // Function to show error message
    function showErrorMessage(message, currentPrice) {
        // Show error in the new prediction section if it exists
        if (hasNewPredictionSection) {
            console.log('Showing error message in new prediction section');
            predictionMain.innerHTML = `
                <div class="alert alert-danger">
                    <strong>Prediction unavailable</strong>
                    <div class="small">Error: ${message}</div>
                    <button id="retry-prediction-btn-main" class="btn btn-outline-danger btn-sm mt-2">
                        <i class="bi bi-arrow-clockwise me-1"></i>Retry
                    </button>
                </div>
            `;

            predictionConfidence.innerHTML = `
                <div class="alert alert-danger">
                    <div class="small">Unable to load confidence scores</div>
                </div>
            `;

            predictionArticles.innerHTML = `
                <div class="alert alert-danger">
                    <div class="small">Unable to load influential articles</div>
                </div>
            `;

            // Add event listener to retry button
            const retryButton = document.getElementById('retry-prediction-btn-main');
            if (retryButton) {
                retryButton.addEventListener('click', function () {
                    showLoading();
                    loadPredictionData(currentPrice);
                });
            }
        }
        // Show error in the old prediction container only if new section doesn't exist
        else if (predictionContainer) {
            console.log('Showing error message in old prediction container (fallback)');
            predictionContainer.innerHTML = `
                <div class="card h-100 border-0">
                    <div class="card-body p-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-baseline justify-content-between">
                                <div class="text-danger">Prediction unavailable</div>
                                <button id="retry-prediction-btn" class="btn btn-outline-primary btn-sm" style="font-size: 0.7rem; padding: 0.15rem 0.4rem;">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                                </button>
                            </div>
                            <div class="small text-danger" style="font-size: 0.75rem;">Error: ${message}</div>
                        </div>
                    </div>
                </div>
            `;

            // Add event listener to retry button
            const retryButton = document.getElementById('retry-prediction-btn');
            if (retryButton) {
                retryButton.addEventListener('click', function () {
                    showLoading();
                    loadPredictionData(currentPrice);
                });
            }
        }
    }

    // Function to render prediction
    function renderPrediction(data) {
        // Get the prediction and confidence
        // Ensure prediction is a string before calling toUpperCase()
        const prediction = data.prediction ? String(data.prediction).toUpperCase() : 'UNKNOWN';
        const confidence = data.confidence ? (data.confidence * 100).toFixed(1) + '%' : 'N/A';

        // Determine color class based on prediction
        let predictionClass = '';
        let predictionIcon = '';
        let predictionBadgeClass = '';

        if (prediction === 'POSITIVE') {
            predictionClass = 'text-success';
            predictionIcon = '<i class="bi bi-graph-up-arrow me-1"></i>';
            predictionBadgeClass = 'bg-success';
        } else if (prediction === 'NEGATIVE') {
            predictionClass = 'text-danger';
            predictionIcon = '<i class="bi bi-graph-down-arrow me-1"></i>';
            predictionBadgeClass = 'bg-danger';
        } else {
            predictionClass = 'text-secondary';
            predictionIcon = '<i class="bi bi-dash me-1"></i>';
            predictionBadgeClass = 'bg-secondary';
        }

        // Get probabilities for each class
        const probabilities = data.probabilities || {};
        console.log('Probabilities from API:', probabilities);

        // Ensure we have actual values for the probabilities, not "N/A"
        const positiveScoreValue = (probabilities.positive !== undefined && probabilities.positive !== null) ? probabilities.positive : 0;
        const negativeScoreValue = (probabilities.negative !== undefined && probabilities.negative !== null) ? probabilities.negative : 0;
        const neutralScoreValue = (probabilities.neutral !== undefined && probabilities.neutral !== null) ? probabilities.neutral : 0;

        console.log('Processed probability values:', {
            positiveScoreValue,
            negativeScoreValue,
            neutralScoreValue
        });

        // Format scores for display
        const positiveScore = (positiveScoreValue * 100).toFixed(1) + '%';
        const negativeScore = (negativeScoreValue * 100).toFixed(1) + '%';
        const neutralScore = (neutralScoreValue * 100).toFixed(1) + '%';

        // Render in the old prediction container only if new section doesn't exist
        if (!hasNewPredictionSection && predictionContainer) {
            console.log('Rendering prediction in old container (fallback)');
            // Create HTML for prediction card
            let html = `
                <div class="card h-100 border-0">
                    <div class="card-body p-2">
                        <div class="d-flex flex-column">
                            <div class="d-flex align-items-baseline justify-content-between">
                                <div class="d-flex align-items-baseline">
                                    <h4 class="me-2 mb-0 ${predictionClass}">${predictionIcon}${prediction}</h4>
                                    <span class="fs-5">${confidence} confidence</span>
                                </div>
                                <button id="run-prediction-btn" class="btn btn-outline-primary btn-sm" style="font-size: 0.7rem; padding: 0.15rem 0.4rem;">
                                    <i class="bi bi-arrow-clockwise me-1"></i>Update
                                </button>
                            </div>

                            <!-- Confidence scores bar chart -->
                            <div class="mt-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="small text-success">Positive</span>
                                    <span class="small text-success">${positiveScore}</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: ${positiveScoreValue * 100}%;"
                                         aria-valuenow="${positiveScoreValue * 100}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-1 mt-2">
                                    <span class="small text-danger">Negative</span>
                                    <span class="small text-danger">${negativeScore}</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-danger" role="progressbar"
                                         style="width: ${negativeScoreValue * 100}%;"
                                         aria-valuenow="${negativeScoreValue * 100}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center mb-1 mt-2">
                                    <span class="small text-secondary">Neutral</span>
                                    <span class="small text-secondary">${neutralScore}</span>
                                </div>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-secondary" role="progressbar"
                                         style="width: ${neutralScoreValue * 100}%;"
                                         aria-valuenow="${neutralScoreValue * 100}"
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add relevant articles button if available
            if (data.relevant_articles && data.relevant_articles.length > 0) {
                html += `
                    <div class="mt-3">
                        <button id="view-articles-btn" class="btn btn-outline-primary btn-sm w-100">
                            <i class="bi bi-newspaper me-1"></i> View Influential Articles (${data.relevant_articles.length})
                        </button>
                    </div>
                `;
            }

            // Add historical accuracy button
            html += `
                <div class="mt-2">
                    <button id="view-history-btn" class="btn btn-outline-secondary btn-sm w-100">
                        <i class="bi bi-graph-up me-1"></i> View Historical Accuracy
                    </button>
                </div>
            `;

            predictionContainer.innerHTML = html;

            // Add event listener to the prediction update button
            const updateButton = document.getElementById('run-prediction-btn');
            if (updateButton) {
                updateButton.addEventListener('click', function () {
                    showLoading();
                    loadPredictionData(currentPriceValue);
                });
            }

            // Add event listener to the view articles button
            const articlesButton = document.getElementById('view-articles-btn');
            if (articlesButton && data.relevant_articles) {
                articlesButton.addEventListener('click', function () {
                    showArticlesModal(data.relevant_articles);
                });
            }

            // Add event listener to the history button
            const historyButton = document.getElementById('view-history-btn');
            if (historyButton) {
                historyButton.addEventListener('click', function () {
                    showHistoryModal();
                });
            }
        }

        // Render in the new prediction section if it exists
        if (hasNewPredictionSection) {
            // Render main prediction
            predictionMain.innerHTML = `
                <div class="d-flex align-items-center">
                    <h3 class="me-2 mb-0 ${predictionClass}">${predictionIcon}${prediction}</h3>
                    <span class="badge ${predictionBadgeClass} fs-6">${confidence}</span>
                </div>
            `;

            // Render confidence scores
            predictionConfidence.innerHTML = `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small text-success fw-bold">Positive</span>
                        <span class="small text-success fw-bold">${positiveScore}</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" role="progressbar"
                             style="width: ${positiveScoreValue * 100}%;"
                             aria-valuenow="${positiveScoreValue * 100}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-1 mt-3">
                        <span class="small text-danger fw-bold">Negative</span>
                        <span class="small text-danger fw-bold">${negativeScore}</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-danger" role="progressbar"
                             style="width: ${negativeScoreValue * 100}%;"
                             aria-valuenow="${negativeScoreValue * 100}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-1 mt-3">
                        <span class="small text-secondary fw-bold">Neutral</span>
                        <span class="small text-secondary fw-bold">${neutralScore}</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-secondary" role="progressbar"
                             style="width: ${neutralScoreValue * 100}%;"
                             aria-valuenow="${neutralScoreValue * 100}"
                             aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>
            `;

            // Render influential articles
            if (data.relevant_articles && data.relevant_articles.length > 0) {
                let articlesHtml = `<div class="list-group">`;

                // Show up to 3 articles in the main view
                const articlesToShow = data.relevant_articles.slice(0, 3);

                articlesToShow.forEach(article => {
                    // Get indicator_prediction class
                    let indicator_predictionClass = 'text-secondary';
                    let indicator_predictionIcon = '<i class="bi bi-dash"></i>';
                    let indicator_predictionText = 'Neutral';

                    if (article.indicator_analysis) {
                        const prediction = article.indicator_analysis.label || 'neutral';
                        if (prediction === 'positive') {
                            indicator_predictionClass = 'text-success';
                            indicator_predictionIcon = '<i class="bi bi-graph-up-arrow"></i>';
                            indicator_predictionText = 'Positive';
                        } else if (prediction === 'negative') {
                            indicator_predictionClass = 'text-danger';
                            indicator_predictionIcon = '<i class="bi bi-graph-down-arrow"></i>';
                            indicator_predictionText = 'Negative';
                        }
                    }

                    // Format date
                    const articleDate = article.publish_time ? formatDate(article.publish_time, true) : 'Unknown date';

                    articlesHtml += `
                        <a href="${article.url || '#'}" class="list-group-item list-group-item-action py-2" target="_blank">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${article.title || 'No title'}</h6>
                                <small class="${indicator_predictionClass}">${indicator_predictionIcon}${indicator_predictionText}</small>
                            </div>
                            <small class="text-muted">
                                ${article.source || 'Unknown source'} - ${articleDate}
                            </small>
                        </a>
                    `;
                });

                articlesHtml += `</div>`;

                if (data.relevant_articles.length > 3) {
                    articlesHtml += `
                        <div class="text-center mt-2">
                            <small class="text-muted">Showing 3 of ${data.relevant_articles.length} articles</small>
                        </div>
                    `;
                }

                predictionArticles.innerHTML = articlesHtml;
            } else {
                predictionArticles.innerHTML = `
                    <div class="alert alert-info">
                        No influential articles available for this prediction.
                    </div>
                `;
            }
        }
    }

    // Function to show articles modal
    function showArticlesModal(articles) {
        // Create modal ID
        const modalId = 'relevantArticlesModal';

        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">Articles Influencing Prediction</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="list-group">
                                ${articles.map(article => {
            // Get indicator_prediction class
            let indicator_predictionClass = 'text-secondary';
            let indicator_predictionIcon = '<i class="bi bi-dash"></i>';
            let indicator_predictionText = 'Neutral';

            if (article.indicator_analysis) {
                const indicator_prediction = article.indicator_analysis.label || 'neutral';
                if (indicator_prediction === 'positive') {
                    indicator_predictionClass = 'text-success';
                    indicator_predictionIcon = '<i class="bi bi-graph-up-arrow"></i>';
                    indicator_predictionText = 'Positive';
                } else if (indicator_prediction === 'negative') {
                    indicator_predictionClass = 'text-danger';
                    indicator_predictionIcon = '<i class="bi bi-graph-down-arrow"></i>';
                    indicator_predictionText = 'Negative';
                }
            }

            // Format date
            const articleDate = article.date ? formatDate(article.date) : 'Unknown date';

            return `
                                        <a href="${article.url || '#'}" class="list-group-item list-group-item-action" target="_blank">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h5 class="mb-1">${article.title || 'No title'}</h5>
                                                <small class="${indicator_predictionClass}">${indicator_predictionIcon} ${indicator_predictionText}</small>
                                            </div>
                                            <p class="mb-1">${article.summary || 'No summary available'}</p>
                                            <small class="text-muted">
                                                ${article.source || 'Unknown source'} - ${articleDate}
                                            </small>
                                        </a>
                                    `;
        }).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page if it doesn't exist
        if (!document.getElementById(modalId)) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    // Function to show history modal
    function showHistoryModal() {
        // Create modal IDs
        const historyModalId = 'predictionHistoryModal';
        const historyChartId = 'predictionHistoryChart';

        // Create modal HTML
        const historyModalHtml = `
            <div class="modal fade" id="${historyModalId}" tabindex="-1" aria-labelledby="${historyModalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${historyModalId}Label">Prediction History & Accuracy</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div id="${historyChartId}" style="height: 300px;"></div>
                            <div id="history-table-container" class="mt-3">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p>Loading prediction history...</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page if it doesn't exist
        if (!document.getElementById(historyModalId)) {
            document.body.insertAdjacentHTML('beforeend', historyModalHtml);
        }

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(historyModalId));
        modal.show();

        // Load prediction history
        loadPredictionHistory();
    }

    // Function to load prediction history
    async function loadPredictionHistory() {
        const historyChartContainer = document.getElementById('predictionHistoryChart');
        const historyTableContainer = document.getElementById('history-table-container');

        if (!historyChartContainer || !historyTableContainer) return;

        try {
            // Fetch prediction history
            const response = await fetch('/api/prediction-history?limit=20');
            const historyData = await response.json();

            if (!historyData || historyData.length === 0) {
                historyTableContainer.innerHTML = '<div class="alert alert-info">No prediction history available yet.</div>';
                return;
            }

            // Prepare data for chart
            const dates = [];
            const positiveScores = [];
            const negativeScores = [];
            const neutralScores = [];

            historyData.forEach(prediction => {
                if (prediction.prediction_date) {
                    // Format date for display
                    const date = new Date(prediction.prediction_date + 'T00:00:00');
                    const formattedDate = date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                    });

                    dates.push(formattedDate);

                    // Get probabilities
                    const probabilities = prediction.probabilities || {};
                    positiveScores.push(probabilities.positive ? probabilities.positive * 100 : 0);
                    negativeScores.push(probabilities.negative ? probabilities.negative * 100 : 0);
                    neutralScores.push(probabilities.neutral ? probabilities.neutral * 100 : 0);
                }
            });

            // Reverse arrays to show oldest to newest
            dates.reverse();
            positiveScores.reverse();
            negativeScores.reverse();
            neutralScores.reverse();

            // Create chart
            Plotly.newPlot(historyChartContainer, [
                {
                    x: dates,
                    y: positiveScores,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Positive',
                    line: { color: 'green' }
                },
                {
                    x: dates,
                    y: negativeScores,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Negative',
                    line: { color: 'red' }
                },
                {
                    x: dates,
                    y: neutralScores,
                    type: 'scatter',
                    mode: 'lines+markers',
                    name: 'Neutral',
                    line: { color: 'gray' }
                }
            ], {
                title: 'Prediction Confidence History',
                xaxis: { title: 'Date' },
                yaxis: { title: 'Confidence (%)', range: [0, 100] },
                margin: { l: 50, r: 50, b: 50, t: 50, pad: 4 },
                legend: { orientation: 'h', y: -0.2 }
            }, {
                responsive: true
            });

            // Create history table
            let tableHtml = `
                <h5 class="mt-4">Recent Predictions</h5>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Prediction</th>
                                <th>Confidence</th>
                                <th>Positive</th>
                                <th>Negative</th>
                                <th>Neutral</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Add rows for each prediction
            historyData.forEach(prediction => {
                const predictionDate = formatDate(prediction.prediction_date);
                // Ensure prediction is a string before calling toUpperCase()
                const predictionText = prediction.prediction ? String(prediction.prediction).toUpperCase() : 'UNKNOWN';
                const confidence = prediction.confidence ? (prediction.confidence * 100).toFixed(1) + '%' : 'N/A';

                // Get probabilities
                const probabilities = prediction.probabilities || {};
                const positiveScore = probabilities.positive ? (probabilities.positive * 100).toFixed(1) + '%' : 'N/A';
                const negativeScore = probabilities.negative ? (probabilities.negative * 100).toFixed(1) + '%' : 'N/A';
                const neutralScore = probabilities.neutral ? (probabilities.neutral * 100).toFixed(1) + '%' : 'N/A';

                // Determine class for prediction
                let predictionClass = '';
                if (predictionText === 'POSITIVE') {
                    predictionClass = 'text-success';
                } else if (predictionText === 'NEGATIVE') {
                    predictionClass = 'text-danger';
                }

                tableHtml += `
                    <tr>
                        <td>${predictionDate}</td>
                        <td class="${predictionClass}">${predictionText}</td>
                        <td>${confidence}</td>
                        <td>${positiveScore}</td>
                        <td>${negativeScore}</td>
                        <td>${neutralScore}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            historyTableContainer.innerHTML = tableHtml;
        } catch (error) {
            console.error('Error loading prediction history:', error);
            historyTableContainer.innerHTML = `
                <div class="alert alert-danger">
                    Error loading prediction history: ${error.message}
                </div>
            `;
        }
    }

    // Helper function to format date
    function formatDate(dateStr, compact = false) {
        try {
            // Parse the date string and handle timezone issues
            let dateToUse = dateStr;
            if (dateStr.indexOf('T') === -1) {
                // If there's no time part, add it to ensure consistent parsing
                dateToUse = `${dateStr}T12:00:00`;
            }

            const date = new Date(dateToUse);

            // Format the date based on the compact parameter
            if (compact) {
                // Compact format: "Jan 1, 2023"
                return date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric'
                });
            } else {
                // Full format: "Monday, January 1, 2023"
                return date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            }
        } catch (e) {
            console.error(`Error formatting date: ${e}`);
            return dateStr;
        }
    }

    // Register the function with the global NewsMonitor object
    window.NewsMonitor.loadPrediction = function (currentPrice) {
        console.log('loadPrediction called with price:', currentPrice);
        currentPriceValue = currentPrice;

        // Load prediction data
        console.log('Loading prediction data');
        loadPredictionData(currentPrice);
    };
});
