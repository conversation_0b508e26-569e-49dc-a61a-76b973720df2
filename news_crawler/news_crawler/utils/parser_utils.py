import json
import logging
import re
from typing import List

from bs4 import BeautifulSoup

from utils.timezone_utils import parse_date_with_timezone
# Configure logger
logger = logging.getLogger(__name__)


def extract_time(soup: BeautifulSoup, properties: List[str]) -> str | None:
    date_str = None

    # Try JSON-LD data
    if not date_str:
        date_str = extract_from_json_ld(soup, properties)
        if date_str:
            logger.debug(f"Found date in JSON-LD: {date_str}")

    # Try time tags and meta tags
    if not date_str:
        for property in properties:
            time_tag = soup.select_one(
                f'time[property="{property}"], time[itemprop="{property}"]')
            if time_tag:
                date_str = time_tag.get('datetime') or time_tag.get(
                    'timestamp') or time_tag.get('data-timestamp')
                if date_str:
                    logger.debug(
                        f"Found date in time tag datetime: {date_str}")
                    break

            time_tag = soup.select_one(
                f'meta[property="{property}"], meta[itemprop="{property}"]')
            if time_tag:
                if time_tag.get('content'):
                    date_str = time_tag['content']
                    logger.debug(
                        f"Found date in meta tag content: {date_str}")
                    break

    # Try general time tag
    if not date_str:
        time_tags = soup.find_all('time')
        for time_tag in time_tags:
            if time_tag.get('datetime'):
                date_str = time_tag['datetime']
                logger.debug(f"Found date in general time tag: {date_str}")

    if date_str:
        # Use our timezone utility to parse the date and convert to EST
        date_str, _ = parse_date_with_timezone(
            date_str, default_timezone='US/Eastern')

    return date_str


def extract_from_json_ld(soup: BeautifulSoup, properties: List[str]) -> str | None:
    """Extract data from JSON-LD scripts"""
    json_ld_scripts = soup.find_all('script', type='application/ld+json')

    for script in json_ld_scripts:
        # try:
        data = json.loads(script.string)

        # Check for each property
        for prop in properties:
            if prop in data:
                value = data[prop]

                # Handle different data types
                if isinstance(value, dict) and 'name' in value:
                    return value['name']
                elif isinstance(value, list) and len(value) > 0:
                    if isinstance(value[0], dict) and 'name' in value[0]:
                        return value[0]['name']
                    else:
                        return value
                else:
                    return value

        # Check in @graph array
        if '@graph' in data:
            for item in data['@graph']:
                for prop in properties:
                    if prop in item:
                        value = item[prop]

                        # Handle different data types
                        if isinstance(value, dict) and 'name' in value:
                            return value['name']
                        elif isinstance(value, list) and len(value) > 0:
                            if isinstance(value[0], dict) and 'name' in value[0]:
                                return value[0]['name']
                            else:
                                return value
                        else:
                            return value

        # except Exception as e:
        #     logger.error(f"Error parsing JSON-LD: {e}")
        #     continue

    return None
