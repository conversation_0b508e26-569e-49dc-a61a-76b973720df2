from datetime import datetime
from typing import Any

import scrapy
from bs4 import BeautifulSoup
import re

from scrapy.http import Response

from news_crawler.items import NewsItem

from news_crawler.utils.parser_utils import extract_time


class YahooFinanceSpider(scrapy.Spider):
    name = 'test'
    allowed_domains = ['finance.yahoo.com']
    start_urls = [
        'https://finance.yahoo.com/news/',
    ]
    custom_settings = {
        'FEEDS': {
            'yahoo_finance_articles.json': {
                'format': 'json',
                'overwrite': True
            }
        },
        'LOG_LEVEL': 'DEBUG',
        'ITEM_PIPELINES': {},
        'SPLASH_URL': 'http://localhost:8050',
        'DOWNLOADER_MIDDLEWARES': {
            'scrapy_splash.SplashCookiesMiddleware': 723,
            'scrapy_splash.SplashMiddleware': 725,
            'scrapy.downloadermiddlewares.httpcompression.HttpCompressionMiddleware': 810,
        },
        'SPIDER_MIDDLEWARES': {
            'scrapy_splash.SplashDeduplicateArgsMiddleware': 100,
        },
        'DUPEFILTER_CLASS': 'scrapy_splash.SplashAwareDupeFilter',
        'HTTPCACHE_STORAGE': 'scrapy_splash.SplashAwareFSCacheStorage',
    }

    def start_requests(self):
        for url in self.start_urls:
            if url.endswith('/news/'):
                script = """
                    function main(splash, args)
                        splash.private_mode_enabled = false
                        splash:set_user_agent("Mozilla/5.0")
                        assert(splash:go(args.url))
                        splash:wait(2)
                        local scroll_to = splash:jsfunc("function() { window.scrollTo(0, document.body.scrollHeight); }")
                        for i = 1, 10 do
                            scroll_to()
                            splash:wait(2)
                        end
                        return { html = splash:html(), url = splash:url() }
                    end
                """
                yield scrapy.Request(
                    url,
                    callback=self.parse,
                    meta={
                        'splash': {
                            'args': {'lua_source': script},
                            'endpoint': 'execute',
                        }
                    }
                )
            else:
                yield scrapy.Request(url, callback=self.parse)

    def parse(self, response: Response, **kwargs: Any) -> Any:
        soup = BeautifulSoup(response.text, 'lxml')

        # Extract article links from stream items
        for item in soup.select('li.stream-item.story-item a.subtle-link[href]'):
            href = item['href']
            if href and re.search(r'/(news|video)/.*-\d+\.html', href):
                yield response.follow(href, callback=self.parse_article, meta={'from_url': response.url})

    def parse_article(self, response):
        self.logger.info(
            f"Fetched article: {response.url}, from {response.meta.get('from_url')}")

        # Create a NewsItem
        item = NewsItem()
        item['url'] = response.url
        item['source'] = self.name

        # Create BeautifulSoup object for extraction
        soup = BeautifulSoup(response.text, 'html.parser')

        # Extract title
        title = soup.find('title')
        if title:
            item['title'] = title.get_text().strip()
        else:
            item['title'] = ""
            self.logger.warning(f"No title found for article: {response.url}")

        # Extract author
        author = soup.select_one('div.byline-attr-author a')
        if author:
            item['author'] = author.get_text().strip()
        else:
            author = soup.select_one('div.byline-attr-author')
            if author:
                item['author'] = author.get_text().strip()
            else:
                item['author'] = ""
                self.logger.warning(
                    f"No author found for article: {response.url}")

        # Extract publish time
        publish_time = extract_time(soup, ['datePublished, dateCreated'])
        if publish_time:
            item['publish_time'] = publish_time
        else:
            item['publish_time'] = ""
            self.logger.warning(
                f"No publish time found for article: {response.url}")

        # Extract update time
        update_time = extract_time(soup, ['dateUpdated, dateModified'])
        if update_time:
            item['update_time'] = update_time
        else:
            item['update_time'] = ""
            self.logger.warning(
                f"No update time found for article: {response.url}")

        # Extract content
        content = ""
        content_container = soup.find("div", class_="body-wrap")
        if content_container:
            for div in content_container.find_all("div", class_=["liveblog-posts", "liveblog-showmore"]):
                div.decompose()
            paragraphs = content_container.find_all("p")
            content = '\n'.join([p.get_text().strip() for p in paragraphs])

        if content:
            item['content'] = content
        else:
            self.logger.warning(
                f"No content found for article: {response.url}")
            item['content'] = ""

        # Extract tags
        tags = []

        # Try category spans for tags
        category_spans = soup.select(
            'span.caas-category-card-label, .article-header span')
        if category_spans:
            tags = [span.text.strip()
                    for span in category_spans if span.text.strip()]
            if tags:
                self.logger.debug(f"Found categories: {tags}")

        # Try meta keywords as fallback
        if not tags:
            keywords = soup.find('meta', attrs={'name': 'keywords'})
            if keywords and keywords.get('content'):
                tags = [tag.strip()
                        for tag in keywords['content'].split(',') if tag.strip()]
                if tags:
                    self.logger.debug(f"Found tags from keywords: {tags}")

        item['tags'] = tags

        # Record crawl time
        item['crawl_time'] = datetime.now().isoformat()

        self.logger.info(
            f"Parsed article: {item.get('title', 'Unkown title')}")

        yield item
