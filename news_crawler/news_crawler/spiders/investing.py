import re
from datetime import datetime, timezone
from typing import Any

import scrapy
from bs4 import BeautifulSoup
from news_crawler.items import NewsItem
from news_crawler.utils.parser_utils import extract_from_json_ld, extract_time
from scrapy.http import Response


class InvestingSpider(scrapy.Spider):
    name = 'investing'
    allowed_domains = ['investing.com']
    start_urls = [
        "https://www.investing.com/news/economy",
        "https://www.investing.com/news/economic-indicators",
        "https://www.investing.com/news/commodities-news"
    ]

    def parse(self, response: Response, **kwargs: Any) -> Any:
        soup = BeautifulSoup(response.text, 'lxml')

        # Directly select all article title links
        for a_tag in soup.select('a[data-test="article-title-link"]'):
            href = a_tag.get('href')
            if href and re.search(r'/news/.*-\d+(?:\.html)?$', href):
                yield response.follow(href, callback=self.parse_article,
                                      meta={'from_url': response.url})

        # Handle pagination
        current_page_match = re.search(
            r'/news/(economy|economic-indicators|commodities-news)/(\d+)', response.url)
        current_page = int(current_page_match.group(2)
                           ) if current_page_match else 1
        next_page_num = float('inf')
        next_page_href = None

        for a in soup.find_all('a', href=True):
            href = a['href']
            match = re.search(
                r'^/news/(economy|economic-indicators|commodities-news)/(\d+)$', href)
            if match:
                page_num = int(match.group(2))
                if current_page < page_num < next_page_num:
                    next_page_num = page_num
                    next_page_href = href

        if next_page_href:
            yield response.follow(next_page_href, callback=self.parse)

    def parse_article(self, response):
        self.logger.info(
            f"Fetched article: {response.url}, from {response.meta.get('from_url')}")

        # Create a NewsItem
        item = NewsItem()
        item['url'] = response.url
        item['source'] = self.name

        # Parse using BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')

        # Extract publish time
        publish_time = extract_time(soup, ['datePublished', 'dateCreated'])
        if publish_time:
            item['publish_time'] = publish_time
        else:
            item['publish_time'] = ""
            self.logger.warning(
                f"No publish time found for article: {response.url}")

        # Extract update time
        update_time = extract_time(soup, ['dateUpdated', 'dateModified'])
        if update_time:
            item['update_time'] = update_time
        else:
            item['update_time'] = ""
            self.logger.warning(
                f"No update time found for article: {response.url}")

        # Extract title
        # Try different selectors for title
        item['title'] = ''
        title_selectors = [
            ('meta[name="description"]',
             lambda e: e.get('content', '').strip()),
            ('h1.articleTitle', lambda e: e.get_text().strip()),
            ('h1', lambda e: e.get_text().strip())
        ]

        for selector, extractor in title_selectors:
            element = soup.select_one(selector)
            if element:
                title = extractor(element)
                if title:
                    item['title'] = title
                    break

        # Extract author
        author = extract_from_json_ld(soup, ['author', 'editor'])
        if author:
            self.logger.debug(f"Found author in JSON-LD: {author}")
            item['author'] = author
        else:
            item['author'] = ""

        # Extract content
        content = ""
        content_element = soup.select('div.article_container p')
        if content_element:
            for e in content_element:
                line = e.get_text().strip()
                if not line:
                    continue
                content += line + '\n'
            content = content.strip()

        # If content found, add it to the item
        if content:
            item['content'] = content
            self.logger.debug(f"Content length: {len(content)}")
        else:
            item['content'] = ""
            self.logger.warning(
                f"No content found for article: {response.url}")

        # Extract tags
        tags_div = soup.select_one('div.articlePage div.tags')
        if tags_div:
            tag_links = tags_div.select('a')
            if tag_links:
                item['tags'] = [tag.get_text().strip()
                                for tag in tag_links if tag.get_text().strip()]
        else:
            item['tags'] = []

        # Record crawl time
        item['crawl_time'] = datetime.now(timezone.utc).isoformat()

        self.logger.info(
            f"Parsed article: {item.get('title', 'Unknown title')}")

        yield item
