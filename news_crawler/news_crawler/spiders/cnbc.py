from datetime import datetime, timezone
from typing import Any
import re

import scrapy
from bs4 import BeautifulSoup
from scrapy.http import Response

from news_crawler.items import NewsItem
from news_crawler.utils.parser_utils import extract_time


class CnbcSpider(scrapy.Spider):
    name = 'cnbc'
    allowed_domains = ['cnbc.com']
    start_urls = [
        "https://www.cnbc.com/economy/",
        "https://www.cnbc.com/markets/",
        "https://www.cnbc.com/finance/"
    ]

    def parse(self, response: Response, **kwargs: Any) -> Any:
        self.logger.info(f"Scraping: {response.url}")
        soup = BeautifulSoup(response.text, 'lxml')
        # Extract and follow article links
        for a_tag in soup.select(
                'div.Card-standardBreakerCard a, div.Card-textContent a'):
            href = a_tag.get('href')
            if href and re.search(r'cnbc\.com/\d{4}/\d{2}/\d{2}/.+\.html',
                                  href):
                yield response.follow(
                    href,
                    callback=self.parse_article,
                    meta={'from_url': response.url}
                )

        # Follow pagination (Load More button)
        next_page = response.css('a.LoadMoreButton-loadMore::attr(href)').get()
        if next_page:
            yield response.follow(next_page, callback=self.parse)

    def parse_article(self, response):
        self.logger.info(
            f"Fetched article: {response.url}, from {response.meta.get('from_url')}")

        # Create a NewsItem
        item = NewsItem()
        item['url'] = response.url
        item['source'] = self.name

        # Parse using BeautifulSoup
        soup = BeautifulSoup(response.text, 'lxml')

        # Extract title
        title_element = soup.select_one(
            'h1.ArticleHeader-headline') or soup.select_one(
            'h1.LiveBlogHeader-headline')
        if title_element:
            item['title'] = title_element.get_text().strip()
        else:
            item['title'] = ""

        # Extract author
        author_element = soup.select_one(
            'a.Author-authorName') or soup.select_one('a.Author-authorInfo')
        if author_element:
            item['author'] = author_element.get_text().strip()
        else:
            item['author'] = ""

        # Extract publish time
        publish_time = extract_time(soup, ['datePublished', 'dateCreated'])
        if publish_time:
            item['publish_time'] = publish_time
        else:
            item['publish_time'] = ""
            self.logger.warning(
                f"No publish time found for article: {response.url}")

        # Extract update time
        update_time = extract_time(soup, ['dateUpdated', 'dateModified'])
        if update_time:
            item['update_time'] = update_time
        else:
            item['update_time'] = ""
            self.logger.warning(
                f"No update time found for article: {response.url}")

        # Extract content
        content_elements = soup.select(
            'div.ArticleBody-articleBody p') or soup.select(
            'div.FeaturedContent-articleBody p')
        if content_elements:
            item['content'] = '\n'.join(
                [p.get_text().strip() for p in content_elements if
                 p.get_text().strip()])
        else:
            item['content'] = ""

        # Extract categories and tags
        tags = []
        category_elements = soup.select('a.ArticleHeader-eyebrow')
        if category_elements:
            categories = [cat.get_text().strip()
                          for cat in category_elements if
                          cat.get_text().strip()]
            if categories:
                self.logger.debug(f"Found tags from categories: {categories}")
                tags = categories

        # Try meta keywords as fallback
        if not tags:
            keywords = soup.find('meta', attrs={'name': 'keywords'})
            if keywords and keywords.get('content'):
                tags = [tag.strip()
                        for tag in keywords['content'].split(',') if
                        tag.strip()]
                if tags:
                    self.logger.debug(f"Found tags from keywords: {tags}")
        item['tags'] = tags

        # Record crawl time
        item['crawl_time'] = datetime.now(timezone.utc).isoformat()

        self.logger.info(
            f"Parsed article: {item.get('title', 'Unknown title')}")

        yield item
