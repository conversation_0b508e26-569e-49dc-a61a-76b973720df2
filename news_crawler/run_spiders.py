#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run all spiders in the news_crawler project.

This script provides functions for running all spiders in the news_crawler project.
It handles command-line arguments, logging configuration, and crawler process setup.
"""

import os
import argparse
import sys
from datetime import datetime
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings

# Import shared logging configuration
from utils.logging_config import configure_logging

# Configure logging
logger = configure_logging(__name__, log_file='crawler.log')


def run_spiders(spiders=None, start_date=None, log_level=None, limit=None):
    """
    Run all spiders in the news_crawler project

    Args:
        start_date (str): Optional start date in YYYY-MM-DD format.
                         If provided, only crawl articles from this date onwards.
        log_level (str): Optional logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL).
                         If provided, overrides the default logging level.
    """
    # Configure logging if a level is specified
    if log_level:
        from utils.logging_config import configure_logging
        global logger
        logger = configure_logging(
            __name__, log_level=log_level, component='crawler')

    # Log start time
    start_time = datetime.now()

    logger.info(f"Starting crawler at {start_time}")
    if start_date:
        logger.info(f"Crawling articles from {start_date} onwards")

    # Get project settings
    settings = get_project_settings()

    # Add start date to settings if provided
    if start_date:
        settings.set('START_DATE', start_date)
        logger.info(f"Setting START_DATE to {start_date}")

    # Set log level in Scrapy settings if provided
    if log_level:
        settings.set('LOG_LEVEL', log_level)
        logger.info(f"Setting Scrapy LOG_LEVEL to {log_level}")

    if limit:
        settings.set('CLOSESPIDER_ITEMCOUNT', limit)
        logger.info(f"Setting CLOSESPIDER_ITEMCOUNT to {limit}")

    # Create crawler process
    process = CrawlerProcess(settings)

    spiders = spiders.split(',') if spiders else None
    if spiders is None or 'yahoo_finance' in spiders:
        from news_crawler.spiders.yahoo_finance import YahooFinanceSpider
        process.crawl(YahooFinanceSpider)
    if spiders is None or 'cnbc' in spiders:
        from news_crawler.spiders.cnbc import CnbcSpider
        process.crawl(CnbcSpider)
    if spiders is None or 'investing' in spiders:
        from news_crawler.spiders.investing import InvestingSpider
        process.crawl(InvestingSpider)

    # Start the crawling process
    process.start()  # This will block until all spiders are finished

    # Log end time
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(f"Crawler finished at {end_time}")
    logger.info(f"Total duration: {duration}")


def main():
    """Entry point for the package."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description='Run spiders in the news_crawler project')
    parser.add_argument('--spiders', type=str,
                        help='Spiders to run. Split by comma. Default: all')
    parser.add_argument('--start-date', type=str,
                        help='Start date in YYYY-MM-DD format')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        default='INFO', help='Set the logging level (default: INFO)')
    parser.add_argument('--limit', type=int,
                        help='Limit the number of items to crawl')
    args = parser.parse_args()

    # Run spiders with the provided arguments
    run_spiders(
        spiders=args.spiders,
        start_date=args.start_date,
        log_level=args.log_level,
        limit=args.limit
    )
    return 0


if __name__ == "__main__":
    main()
