"""
Unit tests for the feature extraction framework.

This module tests the functionality of the feature extraction classes:
- BaseFeatureExtractor
- NewsFeatureExtractor
- PriceFeatureExtractor
- FeatureExtractor
"""

from predictor.config import (
    DATE_FORMAT,
    SP500_TICKER,
    ARTICLE_FEATURE_DAYS,
    PRICE_FEATURE_DAYS,
    FEATURE_EXTRACTION_CONFIG
)
from predictor.data.feature_extraction import (
    NewsFeatureExtractor,
    PriceFeatureExtractor,
    FeatureExtractor
)
from predictor.data.price_dataset import PriceDataset
from predictor.data.news_dataset import NewsDataset
import unittest
import os
import sys
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root directory to the Python path
sys.path.append(str(Path(__file__).resolve().parent.parent.parent))

# Import the classes to test

# Import configuration


class TestFeatureExtraction(unittest.TestCase):
    """Test cases for the feature extraction framework."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are used for all test methods."""
        # Define date range for testing
        cls.end_date = datetime.now().strftime(DATE_FORMAT)
        cls.start_date = (datetime.now() - timedelta(days=30)
                          ).strftime(DATE_FORMAT)

        # Create news and price datasets
        cls.news_dataset = NewsDataset(
            start_date=cls.start_date, end_date=cls.end_date)
        cls.price_dataset = PriceDataset(
            start_date=cls.start_date, end_date=cls.end_date, ticker=SP500_TICKER)

        # Create feature extractor configurations
        cls.news_config = FEATURE_EXTRACTION_CONFIG['news_config'].copy()
        cls.price_config = FEATURE_EXTRACTION_CONFIG['price_config'].copy()

        # Create feature extractors
        cls.news_extractor = NewsFeatureExtractor(
            news_dataset=cls.news_dataset,
            config=cls.news_config
        )

        cls.price_extractor = PriceFeatureExtractor(
            price_dataset=cls.price_dataset,
            config=cls.price_config
        )

        # Create unified feature extractor
        cls.feature_extractor = FeatureExtractor(
            config={
                'use_news_features': True,
                'use_price_features': True,
                'news_config': cls.news_config,
                'price_config': cls.price_config
            },
            news_dataset=cls.news_dataset,
            price_dataset=cls.price_dataset
        )

        # Define test date
        cls.test_date = (datetime.now() - timedelta(days=5)
                         ).strftime(DATE_FORMAT)

    def test_news_feature_extractor(self):
        """Test the NewsFeatureExtractor class."""
        # Extract features for a specific date
        features = self.news_extractor.extract_features_for_date(
            self.test_date)

        # Check that features dictionary has expected keys
        self.assertIsInstance(features, dict)
        self.assertIn('date', features)
        self.assertEqual(features['date'], self.test_date)

        # Check article counts
        if 'article_counts' in features:
            article_counts = features['article_counts']
            self.assertIsInstance(article_counts, dict)

            # Check that article counts are dictionaries with expected keys
            for period in self.news_config['lookback_periods']:
                if period in article_counts:
                    period_counts = article_counts[period]
                    self.assertIsInstance(period_counts, dict)
                    self.assertIn('positive', period_counts)
                    self.assertIn('negative', period_counts)
                    self.assertIn('neutral', period_counts)
                    self.assertIn('total', period_counts)

        # Check sentiment statistics
        if 'sentiment_stats' in features:
            sentiment_stats = features['sentiment_stats']
            self.assertIsInstance(sentiment_stats, dict)
            self.assertIn('mean', sentiment_stats)
            self.assertIn('median', sentiment_stats)
            self.assertIn('std', sentiment_stats)
            self.assertIn('min', sentiment_stats)
            self.assertIn('max', sentiment_stats)

        # Prepare features for model
        prepared_features = self.news_extractor.prepare_features_for_model([
                                                                           features])
        self.assertIsInstance(prepared_features, dict)
        self.assertIn('X', prepared_features)
        self.assertIn('feature_names', prepared_features)
        self.assertIn('dates', prepared_features)

        # Check that X is a NumPy array
        X = prepared_features['X']
        self.assertIsInstance(X, np.ndarray)

        # Check that feature_names is a list
        feature_names = prepared_features['feature_names']
        self.assertIsInstance(feature_names, list)

        # Check that dates is a list
        dates = prepared_features['dates']
        self.assertIsInstance(dates, list)

        # Check that the number of features matches the length of feature_names
        if X.size > 0:
            self.assertEqual(X.shape[1], len(feature_names))

    def test_price_feature_extractor(self):
        """Test the PriceFeatureExtractor class."""
        # Extract features for a specific date
        features = self.price_extractor.extract_features_for_date(
            self.test_date)

        # Check that features dictionary has expected keys
        self.assertIsInstance(features, dict)
        self.assertIn('date', features)
        self.assertEqual(features['date'], self.test_date)

        # Check price features
        if 'price_features' in features:
            price_features = features['price_features']
            self.assertIsInstance(price_features, np.ndarray)

        # Check return rates
        if 'returns' in features:
            returns = features['returns']
            self.assertIsInstance(returns, dict)

            # Check that return rates are dictionaries with expected keys
            for period in self.price_config['lookback_periods']:
                period_key = f'{period}d'
                if period_key in returns:
                    self.assertIsInstance(returns[period_key], float)

        # Check technical indicators
        if 'indicators' in features:
            indicators = features['indicators']
            self.assertIsInstance(indicators, dict)

            # Check that indicators are dictionaries with expected keys
            for indicator in self.price_config['indicators']:
                if indicator == 'sma':
                    for period in [5, 10, 20]:
                        indicator_key = f'sma_{period}'
                        if indicator_key in indicators:
                            self.assertIsInstance(
                                indicators[indicator_key], float)

                elif indicator == 'ema':
                    for period in [5, 10, 20]:
                        indicator_key = f'ema_{period}'
                        if indicator_key in indicators:
                            self.assertIsInstance(
                                indicators[indicator_key], float)

                elif indicator == 'rsi':
                    if 'rsi_14' in indicators:
                        self.assertIsInstance(indicators['rsi_14'], float)

                elif indicator == 'volatility':
                    for period in [5, 10, 20]:
                        indicator_key = f'volatility_{period}'
                        if indicator_key in indicators:
                            self.assertIsInstance(
                                indicators[indicator_key], float)

        # Prepare features for model
        prepared_features = self.price_extractor.prepare_features_for_model([
                                                                            features])
        self.assertIsInstance(prepared_features, dict)
        self.assertIn('X', prepared_features)
        self.assertIn('feature_names', prepared_features)
        self.assertIn('dates', prepared_features)

        # Check that X is a NumPy array
        X = prepared_features['X']
        self.assertIsInstance(X, np.ndarray)

        # Check that feature_names is a list
        feature_names = prepared_features['feature_names']
        self.assertIsInstance(feature_names, list)

        # Check that dates is a list
        dates = prepared_features['dates']
        self.assertIsInstance(dates, list)

        # Check that the number of features matches the length of feature_names
        if X.size > 0:
            self.assertEqual(X.shape[1], len(feature_names))

    def test_unified_feature_extractor(self):
        """Test the FeatureExtractor class."""
        # Extract features for a specific date
        features = self.feature_extractor.extract_features_for_date(
            self.test_date)

        # Check that features dictionary has expected keys
        self.assertIsInstance(features, dict)
        self.assertIn('date', features)
        self.assertEqual(features['date'], self.test_date)

        # Check that features include both news and price features
        has_news_features = 'article_counts' in features or 'sentiment_scores' in features
        has_price_features = 'price_features' in features or 'returns' in features

        # At least one of news or price features should be present
        self.assertTrue(has_news_features or has_price_features)

        # Prepare features for model
        X, y, feature_names = self.feature_extractor.prepare_features_for_model([
                                                                                features])

        # Check that X is a NumPy array
        self.assertIsInstance(X, np.ndarray)

        # Check that feature_names is a list
        self.assertIsInstance(feature_names, list)

        # Check that the number of features matches the length of feature_names
        if X.size > 0:
            self.assertEqual(X.shape[1], len(feature_names))


if __name__ == '__main__':
    unittest.main()
