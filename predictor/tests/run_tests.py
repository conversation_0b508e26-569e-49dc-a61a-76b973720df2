#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run all tests for the SP500 price predictor.
This script will run all test cases and generate output files.
"""

import os
import subprocess
import argparse
from datetime import datetime

# Define paths
TESTS_DIR = os.path.dirname(os.path.abspath(__file__))
INPUT_DIR = os.path.join(TESTS_DIR, "input")
OUTPUT_DIR = os.path.join(TESTS_DIR, "output")

# Define test cases
TEST_CASES = [
    {
        "name": "models_unittest",
        "type": "unittest",
        "module": "predictor.tests.test_models"
    },
    {
        "name": "positive_sentiment",
        "type": "prediction",
        "input": os.path.join(INPUT_DIR, "test_input_positive.json"),
        "output": os.path.join(OUTPUT_DIR, "prediction_positive.json")
    },
    {
        "name": "negative_sentiment",
        "type": "prediction",
        "input": os.path.join(INPUT_DIR, "test_input_negative.json"),
        "output": os.path.join(OUTPUT_DIR, "prediction_negative.json")
    },
    {
        "name": "data_processor",
        "type": "data_processor",
        "output_dir": os.path.join(OUTPUT_DIR, "dataset_test")
    },
    {
        "name": "sentiment_classifier",
        "type": "sentiment_classifier"
    },
    {
        "name": "data_module",
        "type": "unittest",
        "module": "predictor.tests.test_data_module"
    },
    {
        "name": "news_data_processor",
        "type": "news_data_processor",
        "output_dir": os.path.join(OUTPUT_DIR, "news_test")
    },
    {
        "name": "text_processing",
        "type": "text_processing",
        "input": os.path.join(INPUT_DIR, "news", "test_news_articles.json"),
        "output_dir": os.path.join(OUTPUT_DIR, "text_processing")
    },
    {
        "name": "text_processing_spacy",
        "type": "text_processing",
        "input": os.path.join(INPUT_DIR, "news", "test_news_articles.json"),
        "output_dir": os.path.join(OUTPUT_DIR, "text_processing_spacy"),
        "backend": "spacy"
    },
    {
        "name": "text_processing_nltk",
        "type": "text_processing",
        "input": os.path.join(INPUT_DIR, "news", "test_news_articles.json"),
        "output_dir": os.path.join(OUTPUT_DIR, "text_processing_nltk"),
        "backend": "nltk"
    },
    {
        "name": "news_dataset_unittest",
        "type": "unittest",
        "module": "predictor.tests.test_news_data_processor"
    },
    {
        "name": "text_processing_utils_unittest",
        "type": "unittest",
        "module": "predictor.tests.test_text_processing_utils"
    }
]


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run all tests for the SP500 price predictor')
    parser.add_argument('--test', '-t', type=str, default=None,
                        help='Name of a specific test to run (default: run all tests)')
    return parser.parse_args()


def run_test(test_case):
    """Run a single test case."""
    print(f"\n{'='*80}")
    print(f"Running test: {test_case['name']}")
    print(f"{'='*80}")

    # Create timestamp for this run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Build command based on test type
    if test_case.get('type') == 'prediction':
        # Create output filename with timestamp
        output_file = test_case['output'].replace('.json', f'_{timestamp}.json')

        # Build command for prediction test
        cmd = [
            "python", "-m", "predictor.tests.test_custom_prediction",
            "--input", test_case['input'],
            "--output", output_file
        ]
    elif test_case.get('type') == 'data_processor':
        # Build command for data processor test
        output_dir = os.path.join(test_case['output_dir'], f"run_{timestamp}")

        cmd = [
            "python", "-m", "predictor.tests.test_data_processor",
            "--output_dir", output_dir,
            "--lookback_days", "30",  # Use a small value for testing
            "--include_encoded_data"
        ]
    elif test_case.get('type') == 'sentiment_classifier':
        # Build command for sentiment classifier test
        cmd = [
            "python", "-m", "predictor.tests.test_sentiment_classifier"
        ]
    elif test_case.get('type') == 'news_data_processor':
        # Build command for news data processor test
        output_dir = os.path.join(test_case['output_dir'], f"run_{timestamp}")

        cmd = [
            "python", "-m", "predictor.tests.test_news_data_processing",
            "--output_dir", output_dir,
            "--article_feature_days", "3",  # Use a small value for testing
            "--include_encoded_data"
        ]
    elif test_case.get('type') == 'text_processing':
        # Build command for text processing test
        output_dir = os.path.join(test_case['output_dir'], f"run_{timestamp}")
        os.makedirs(output_dir, exist_ok=True)

        cmd = [
            "python", "-m", "predictor.tests.test_text_processing",
            "--input_file", test_case['input'],
            "--output_dir", output_dir
        ]

        # Add backend parameter if specified
        if 'backend' in test_case:
            cmd.extend(["--backend", test_case['backend']])
    elif test_case.get('type') == 'unittest':
        # Build command for unittest
        cmd = [
            "python", "-m", "unittest", test_case['module']
        ]
    else:
        print(f"Unknown test type: {test_case.get('type')}")
        return False

    # Run command
    print(f"Command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)

    # Print output
    print("\nSTDOUT:")
    print(result.stdout)

    if result.stderr:
        print("\nSTDERR:")
        print(result.stderr)

    # Print result
    if result.returncode == 0:
        print(f"\nTest {test_case['name']} completed successfully")
        if test_case.get('type') == 'prediction':
            print(f"Output saved to: {output_file}")
        elif test_case.get('type') == 'data_processor':
            print(f"Output saved to: {output_dir}")
        elif test_case.get('type') == 'news_data_processor':
            print(f"Output saved to: {output_dir}")
        elif test_case.get('type') == 'text_processing':
            print(f"Output saved to: {output_dir}")
        elif test_case.get('type') == 'sentiment_classifier':
            print(f"Output saved to: predictor/tests/output/sentiment_results.json")
        elif test_case.get('type') == 'unittest':
            print(f"Unit tests in {test_case['module']} passed")
    else:
        print(f"\nTest {test_case['name']} failed with return code {result.returncode}")

    return result.returncode == 0


def main():
    """Main function to run all tests."""
    args = parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Track results
    results = {}

    # Run tests
    if args.test:
        # Run a specific test
        for test_case in TEST_CASES:
            if test_case['name'] == args.test:
                results[test_case['name']] = run_test(test_case)
                break
        else:
            print(f"Test '{args.test}' not found")
            return 1
    else:
        # Run all tests
        for test_case in TEST_CASES:
            results[test_case['name']] = run_test(test_case)

    # Print summary
    print(f"\n{'='*80}")
    print("Test Summary")
    print(f"{'='*80}")

    all_passed = True
    for name, passed in results.items():
        status = "PASSED" if passed else "FAILED"
        print(f"{name}: {status}")
        if not passed:
            all_passed = False

    # Return exit code
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit(main())
