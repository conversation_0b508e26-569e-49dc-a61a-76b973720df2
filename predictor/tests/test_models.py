"""
Tests for the SP500 price prediction models.
"""

import os
import unittest
import numpy as np
from datetime import datetime, timedelta

from predictor.models.model_factory import create_model
from predictor.models.logistic_regression_model import LogisticRegressionModel
from predictor.models.gradient_boosting_model import GradientBoostingModel
from predictor.models.lstm_model import LSTMModel


class TestModels(unittest.TestCase):
    """Test cases for the SP500 price prediction models."""

    def setUp(self):
        """Set up test fixtures."""
        # Create sample data
        self.X_train = np.random.rand(100, 10)
        self.y_train = np.random.randint(0, 3, 100)
        self.X_val = np.random.rand(20, 10)
        self.y_val = np.random.randint(0, 3, 20)

        # Create model configurations
        self.logistic_regression_config = {
            'model_type': 'logistic_regression',
            'model_name': 'test_logistic_regression',
            'C': 1.0,
            'max_iter': 100,
            'solver': 'liblinear',
            'class_weight': 'balanced',
            'feature_config': {
                'news_days': 7,
                'price_days': 30,
                'use_sentiment': True,
                'use_price_indicators': False
            }
        }

        self.gradient_boosting_config = {
            'model_type': 'gradient_boosting',
            'model_name': 'test_gradient_boosting',
            'n_estimators': 50,
            'learning_rate': 0.1,
            'max_depth': 3,
            'subsample': 1.0,
            'feature_config': {
                'news_days': 7,
                'price_days': 30,
                'use_sentiment': True,
                'use_price_indicators': False
            }
        }

        self.lstm_config = {
            'model_type': 'lstm',
            'model_name': 'test_lstm',
            'hidden_size': 32,
            'num_layers': 1,
            'dropout': 0.1,
            'input_size': 1,
            'num_classes': 3,
            'feature_config': {
                'news_days': 7,
                'price_days': 30,
                'use_sentiment': True,
                'use_price_indicators': False,
                'sequence_format': True
            }
        }

    def test_create_logistic_regression_model(self):
        """Test creating a logistic regression model."""
        model = create_model(self.logistic_regression_config)
        self.assertIsNotNone(model)
        self.assertIsInstance(model, LogisticRegressionModel)
        self.assertEqual(model.model_type, 'classification')

    def test_create_gradient_boosting_model(self):
        """Test creating a gradient boosting model."""
        model = create_model(self.gradient_boosting_config)
        self.assertIsNotNone(model)
        self.assertIsInstance(model, GradientBoostingModel)
        self.assertEqual(model.model_type, 'classification')

    def test_create_lstm_model(self):
        """Test creating an LSTM model."""
        model = create_model(self.lstm_config)
        self.assertIsNotNone(model)
        self.assertIsInstance(model, LSTMModel)
        self.assertEqual(model.model_type, 'classification')

    def test_logistic_regression_predict(self):
        """Test prediction with logistic regression model."""
        model = create_model(self.logistic_regression_config)
        
        # Train the model
        model.train(self.X_train, self.y_train)
        
        # Make predictions
        predictions = model.predict(self.X_val)
        self.assertEqual(len(predictions), len(self.X_val))
        self.assertTrue(all(0 <= p <= 2 for p in predictions))
        
        # Test predict_with_confidence
        predictions, confidence_scores = model.predict_with_confidence(self.X_val)
        self.assertEqual(len(predictions), len(self.X_val))
        self.assertEqual(len(confidence_scores), len(self.X_val))
        self.assertTrue(all(0 <= c <= 1 for c in confidence_scores))

    def test_gradient_boosting_predict(self):
        """Test prediction with gradient boosting model."""
        model = create_model(self.gradient_boosting_config)
        
        # Train the model
        model.train(self.X_train, self.y_train)
        
        # Make predictions
        predictions = model.predict(self.X_val)
        self.assertEqual(len(predictions), len(self.X_val))
        self.assertTrue(all(0 <= p <= 2 for p in predictions))
        
        # Test predict_with_confidence
        predictions, confidence_scores = model.predict_with_confidence(self.X_val)
        self.assertEqual(len(predictions), len(self.X_val))
        self.assertEqual(len(confidence_scores), len(self.X_val))
        self.assertTrue(all(0 <= c <= 1 for c in confidence_scores))

    def test_lstm_predict(self):
        """Test prediction with LSTM model."""
        # For LSTM, we need sequence data
        X_train_seq = self.X_train.reshape(100, 10, 1)
        X_val_seq = self.X_val.reshape(20, 10, 1)
        
        model = create_model(self.lstm_config)
        
        # Train the model (with minimal epochs for testing)
        model.model_config['num_epochs'] = 1
        model.train(X_train_seq, self.y_train)
        
        # Make predictions
        predictions = model.predict(X_val_seq)
        self.assertEqual(len(predictions), len(self.X_val))
        
        # Test predict_with_confidence
        predictions, confidence_scores = model.predict_with_confidence(X_val_seq)
        self.assertEqual(len(predictions), len(self.X_val))
        self.assertEqual(len(confidence_scores), len(self.X_val))


if __name__ == '__main__':
    unittest.main()
