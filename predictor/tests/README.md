# SP500 Price Predictor Tests

This directory contains tests for the SP500 price predictor.

## Directory Structure

```
predictor/tests/
├── input/
│   ├── test_input_positive.json
│   ├── test_input_negative.json
│   ├── test_articles.json
│   └── news/
│       └── test_news_articles.json
├── output/
│   ├── prediction_positive_*.json
│   ├── prediction_negative_*.json
│   ├── sentiment_results_*.json
│   ├── dataset_test/
│   │   └── run_*/
│   ├── news_test/
│   │   └── run_*/
│   └── text_processing/
│       └── run_*/
├── test_custom_prediction.py
├── test_data_processor.py
├── test_data_module.py
├── test_news_data_processor.py
├── test_news_data_processing.py
├── test_sentiment_classifier.py
├── test_text_processing.py
└── run_tests.py
```

## Running Tests

### Run All Tests

To run all tests:

```bash
python -m predictor.tests.run_tests
```

### Run a Specific Test

To run a specific test:

```bash
python -m predictor.tests.run_tests --test positive_sentiment
```

Available tests:
- `positive_sentiment`: Tests prediction with positive news sentiment
- `negative_sentiment`: Tests prediction with negative news sentiment
- `data_processor`: Tests the data processing pipeline and outputs dataset samples
- `sentiment_classifier`: Tests the FinBERT sentiment classification model on a financial news article
- `news_data_processor`: Tests the news data processing pipeline and outputs processed news data
- `text_processing`: Tests the text processing functions for cleaning, sentence splitting, and duplicate removal
- `text_processing_spacy`: Tests the text processing functions using the spaCy backend
- `text_processing_nltk`: Tests the text processing functions using the NLTK backend
- `text_processing_utils_unittest`: Runs unit tests for the text processing utility module
- `data_module`: Runs unit tests for the data module classes
- `news_dataset_unittest`: Runs unit tests for the NewsDataset class and text processing functions

### Run a Custom Test

To run a custom test with your own input file:

```bash
python -m predictor.tests.test_custom_prediction --input path/to/input.json --output path/to/output.json
```

### Run Sentiment Analysis

To analyze the sentiment of a financial news article:

```bash
# Run directly:
python -m predictor.tests.test_sentiment_classifier

# Or run through the test framework:
python -m predictor.tests.run_tests --test sentiment_classifier
```

The test reads all articles from `predictor/tests/input/test_articles.json`, analyzes their sentiment, and saves the results to `predictor/tests/output/sentiment_results.json`.

## Test Input Format

The test input files should be in JSON format with the following structure:

```json
{
    "model_path": "/path/to/model",
    "prediction_date": "2025-05-13",
    "current_price": 578.90,
    "news_data": {
        "articles_by_date": {
            "2025-05-12": [
                "Article 1 text",
                "Article 2 text",
                "Article 3 text"
            ],
            "2025-05-11": [
                "Article 1 text",
                "Article 2 text",
                "Article 3 text"
            ],
            ...
        }
    },
    "price_data": {
        "data": {
            "2025-05-12": {
                "Open": 581.47,
                "High": 581.73,
                "Low": 577.04,
                "Close": 578.90,
                "Volume": 35091956
            },
            "2025-05-11": {
                "Open": 566.48,
                "High": 567.50,
                "Low": 562.76,
                "Close": 564.34,
                "Volume": 37517500
            },
            ...
        }
    }
}
```

## Test Output Formats

### Price Prediction Output Format

The price prediction test output files are in JSON format with the following structure:

```json
{
    "prediction_date": "2025-05-13",
    "prediction_made_at": "2025-05-12T09:27:06.902605",
    "model_path": "/path/to/model",
    "predicted_price": 547.46,
    "confidence": 1.0,
    "direction": "DOWN",
    "current_price": 578.9,
    "price_change": -31.44,
    "price_change_percentage": -0.0543,
    "price_change_percentage_formatted": "-5.43%"
}
```

### Sentiment Classifier

The sentiment classifier test reads all articles from `predictor/tests/input/test_articles.json` and outputs the sentiment analysis results for each article:

```
Article earnings_positive:
  Text: Stocks rallied today as the company reported better-than...
  Predicted sentiment: positive
  Expected sentiment: positive ✓

Article merger_positive:
  Text: The merger is expected to create significant synergies...
  Predicted sentiment: positive
  Expected sentiment: positive ✓

...

Sentiment Classification Summary:
Total articles analyzed: 12

Accuracy: 75.00% (9/12)

Sentiment distribution:
  positive: 4 (33.33%)
  negative: 5 (41.67%)
  neutral: 3 (25.00%)
```

The test also saves the results to a fixed output file at `predictor/tests/output/sentiment_results.json`:

```json
{
    "timestamp": "2025-05-12T10:30:45.123456",
    "model": "ProsusAI/finbert",
    "num_articles": 12,
    "sentiment_distribution": {
        "positive": 4,
        "negative": 5,
        "neutral": 3
    },
    "accuracy": 0.75,
    "correct_predictions": 9,
    "total_with_expected": 12,
    "results": [
        {
            "id": "earnings_positive",
            "text_preview": "Stocks rallied today as the company reported better-than-expected earnings...",
            "predicted_sentiment": "positive",
            "sentiment_scores": {
                "positive": 0.9564,
                "negative": 0.0197,
                "neutral": 0.0239
            },
            "expected_sentiment": "positive",
            "correct": true
        },
        // More results...
    ]
}
```

To test with different articles, edit the `predictor/tests/input/test_articles.json` file.

### News Data Processing Tests

To test the news data processing functionality:

```bash
# Run directly:
python -m predictor.tests.test_news_data_processing --output_dir predictor/tests/output/news_test

# Or run through the test framework:
python -m predictor.tests.run_tests --test news_data_processor
```

The test loads news articles, processes them, and outputs the processed data to the specified directory.

### Text Processing Tests

To test the text processing functions:

```bash
# Run directly:
python -m predictor.tests.test_text_processing --input_file predictor/tests/input/news/test_news_articles.json

# Or run through the test framework:
python -m predictor.tests.run_tests --test text_processing
```

The test processes each article in the input file and outputs the results of each processing step (cleaning, sentence splitting, duplicate removal) to the specified directory.

You can specify a specific backend to use:

```bash
# Test with spaCy backend:
python -m predictor.tests.test_text_processing --input_file predictor/tests/input/news/test_news_articles.json --backend spacy

# Test with NLTK backend:
python -m predictor.tests.test_text_processing --input_file predictor/tests/input/news/test_news_articles.json --backend nltk

# Test with regex backend:
python -m predictor.tests.test_text_processing --input_file predictor/tests/input/news/test_news_articles.json --backend regex
```

Or run the unit tests for the text processing utility module:

```bash
# Run the unit tests:
python -m unittest predictor.tests.test_text_processing_utils

# Or run through the test framework:
python -m predictor.tests.run_tests --test text_processing_utils_unittest
```
