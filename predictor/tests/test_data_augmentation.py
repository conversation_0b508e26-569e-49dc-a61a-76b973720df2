"""
Tests for the data augmentation module.

This module contains tests for the data augmentation module, including
the base augmentation class, chunk augmentation, and factory functions.
"""

import unittest
from unittest.mock import patch, MagicMock
from typing import Dict, List, Any

from predictor.data.augmentation import (
    BaseAugmentation,
    ChunkAugmentation,
    create_augmentation,
    create_augmentations
)
from predictor.data.news_dataset import NewsDataset


class TestChunkAugmentation(unittest.TestCase):
    """Test cases for the ChunkAugmentation class."""

    def setUp(self):
        """Set up test fixtures."""
        self.augmentation = ChunkAugmentation(min_chunk_size_words=10)

        # Create sample articles with chunks
        self.sample_articles = [
            {
                'url': 'https://example.com/article1',
                'source': 'test',
                'title': 'Test Article 1',
                'publish_time': '2025-05-01T12:00:00-04:00',
                'content': 'This is the content of test article 1.',
                'sentiment': {
                    'sentiment': 'positive',
                    'scores': {
                        'positive': 0.7,
                        'negative': 0.1,
                        'neutral': 0.2
                    }
                },
                'chunks': [
                    {
                        'type': 'title',
                        'text': 'Test Article 1',
                        'word_count': 3,
                        'sentiment_scores': {
                            'positive': 0.6,
                            'negative': 0.1,
                            'neutral': 0.3
                        }
                    },
                    {
                        'type': 'content',
                        'text': 'This is the content of test article 1. It has enough words to be considered valid.',
                        'word_count': 15,
                        'content_chunk_index': 0,
                        'sentiment_scores': {
                            'positive': 0.7,
                            'negative': 0.1,
                            'neutral': 0.2
                        }
                    }
                ]
            },
            {
                'url': 'https://example.com/article2',
                'source': 'test',
                'title': 'Test Article 2',
                'publish_time': '2025-05-02T12:00:00-04:00',
                'content': 'This is the content of test article 2.',
                'sentiment': {
                    'sentiment': 'negative',
                    'scores': {
                        'positive': 0.1,
                        'negative': 0.7,
                        'neutral': 0.2
                    }
                },
                'chunks': [
                    {
                        'type': 'title',
                        'text': 'Test Article 2',
                        'word_count': 3,
                        'sentiment_scores': {
                            'positive': 0.1,
                            'negative': 0.7,
                            'neutral': 0.2
                        }
                    },
                    {
                        'type': 'content',
                        'text': 'This is the content of test article 2. It also has enough words to be valid.',
                        'word_count': 14,
                        'content_chunk_index': 0,
                        'sentiment_scores': {
                            'positive': 0.1,
                            'negative': 0.8,
                            'neutral': 0.1
                        }
                    }
                ]
            }
        ]

        # Sample labels
        self.sample_labels = [0, 1]  # positive, negative

    def test_get_name(self):
        """Test that get_name returns the correct name."""
        self.assertEqual(self.augmentation.get_name(), "chunk")

    def test_is_valid_chunk(self):
        """Test that _is_valid_chunk correctly identifies valid chunks."""
        # Valid chunk
        valid_chunk = {
            'text': 'This is a valid chunk with enough words to meet the minimum requirement.',
            'word_count': 15
        }
        self.assertTrue(self.augmentation._is_valid_chunk(valid_chunk))

        # Invalid chunk - too few words
        invalid_chunk = {
            'text': 'Too few words',
            'word_count': 3
        }
        self.assertFalse(self.augmentation._is_valid_chunk(invalid_chunk))

        # Invalid chunk - no text
        invalid_chunk = {
            'word_count': 15
        }
        self.assertFalse(self.augmentation._is_valid_chunk(invalid_chunk))

    def test_get_chunk_sentiment_score(self):
        """Test that _get_chunk_sentiment_score correctly extracts sentiment scores."""
        # Chunk with sentiment_scores
        chunk = {
            'sentiment_scores': {
                'positive': 0.7,
                'negative': 0.1,
                'neutral': 0.2
            }
        }
        self.assertAlmostEqual(self.augmentation._get_chunk_sentiment_score(chunk), 0.6)

        # Chunk with sentiment_score
        chunk = {
            'sentiment_score': 0.6
        }
        self.assertAlmostEqual(self.augmentation._get_chunk_sentiment_score(chunk), 0.6)

        # Chunk with no sentiment information
        chunk = {}
        self.assertIsNone(self.augmentation._get_chunk_sentiment_score(chunk))

    def test_create_article_from_chunk(self):
        """Test that _create_article_from_chunk correctly creates a new article."""
        article = self.sample_articles[0]
        chunk = article['chunks'][1]  # Content chunk

        chunk_article = self.augmentation._create_article_from_chunk(article, chunk)

        # Check basic metadata
        self.assertEqual(chunk_article['url'], article['url'])
        self.assertEqual(chunk_article['source'], article['source'])
        self.assertEqual(chunk_article['publish_time'], article['publish_time'])
        self.assertTrue(chunk_article['is_augmented'])
        self.assertEqual(chunk_article['augmentation_type'], 'chunk')
        self.assertEqual(chunk_article['original_article_url'], article['url'])
        self.assertEqual(chunk_article['chunk_type'], 'content')
        self.assertEqual(chunk_article['chunk_index'], 0)

        # Check content
        self.assertEqual(chunk_article['content'], chunk['text'])

        # Check sentiment
        self.assertIn('sentiment', chunk_article)
        self.assertAlmostEqual(
            chunk_article['sentiment']['scores']['positive'] -
            chunk_article['sentiment']['scores']['negative'],
            0.6
        )

    def test_augment(self):
        """Test that augment correctly augments articles."""
        augmented_articles, augmented_labels = self.augmentation.augment(
            self.sample_articles, self.sample_labels
        )

        # We should have 2 augmented articles (1 from each original article)
        # The title chunks are too small to be included
        self.assertEqual(len(augmented_articles), 2)
        self.assertEqual(len(augmented_labels), 2)

        # Check that the labels are preserved
        self.assertEqual(augmented_labels[0], 0)  # positive
        self.assertEqual(augmented_labels[1], 1)  # negative

        # Check that the augmented articles have the correct metadata
        self.assertTrue(augmented_articles[0]['is_augmented'])
        self.assertEqual(augmented_articles[0]['augmentation_type'], 'chunk')
        self.assertEqual(augmented_articles[0]['chunk_type'], 'content')

        # Check that the content is from the chunk
        self.assertEqual(
            augmented_articles[0]['content'],
            self.sample_articles[0]['chunks'][1]['text']
        )


class TestAugmentationFactory(unittest.TestCase):
    """Test cases for the augmentation factory functions."""

    def test_create_augmentation(self):
        """Test that create_augmentation creates the correct augmentation."""
        # Create a chunk augmentation
        augmentation = create_augmentation('chunk')
        self.assertIsInstance(augmentation, ChunkAugmentation)
        self.assertEqual(augmentation.get_name(), 'chunk')

        # Try to create an unsupported augmentation
        augmentation = create_augmentation('unsupported')
        self.assertIsNone(augmentation)

    def test_create_augmentations(self):
        """Test that create_augmentations creates multiple augmentations."""
        # Create all augmentations
        augmentations = create_augmentations(['chunk'])
        self.assertEqual(len(augmentations), 1)
        self.assertIsInstance(augmentations[0], ChunkAugmentation)

        # Create with default techniques
        with patch('predictor.data.augmentation.factory.AUGMENTATION_TECHNIQUES', ['chunk']):
            augmentations = create_augmentations()
            self.assertEqual(len(augmentations), 1)
            self.assertIsInstance(augmentations[0], ChunkAugmentation)


if __name__ == '__main__':
    unittest.main()
