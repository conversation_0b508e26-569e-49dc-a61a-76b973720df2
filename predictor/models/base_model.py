"""
Base model interface for SP500 price prediction.
Defines the common interface for all prediction models.
"""

import os
import json
import pickle
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any, Union

import numpy as np
from sklearn.preprocessing import StandardScaler

from predictor.config import MODELS_DIR
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__, component='predictor')


class BasePredictionModel(ABC):
    """
    Base class for all prediction models.
    Defines the common interface that all models must implement.
    """

    def __init__(self, model_config: Dict[str, Any]):
        """
        Initialize the base prediction model.

        Args:
            model_config: Dictionary containing model configuration parameters.
        """
        self.model_config = model_config
        self.model_name = model_config.get('model_name', 'base_model')
        self.model_type = model_config.get('model_type', 'classification')
        self.feature_config = model_config.get('feature_config', {})
        self.price_scaler = StandardScaler()
        self.model = None

    @abstractmethod
    def build_model(self) -> Any:
        """
        Build the model architecture.
        Must be implemented by subclasses.

        Returns:
            The model instance.
        """
        pass

    @abstractmethod
    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, float]:
        """
        Train the model.

        Args:
            X_train: Training features.
            y_train: Training labels.
            X_val: Validation features.
            y_val: Validation labels.
            output_dir: Directory to save the model.

        Returns:
            Dictionary of evaluation metrics.
        """
        pass

    @abstractmethod
    def predict(
        self,
        X: np.ndarray
    ) -> np.ndarray:
        """
        Make predictions.

        Args:
            X: Input features.

        Returns:
            Predicted values.
        """
        pass

    def predict_with_confidence(
        self,
        X: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make predictions with confidence scores and class probabilities.

        Args:
            X: Input features.

        Returns:
            Tuple of (predictions, probabilities).
            - predictions: Array of predicted class labels
            - probabilities: Array of class probabilities for each sample
        """
        # Default implementation - override in subclasses for model-specific confidence
        predictions = self.predict(X)

        # For classification models, create default probabilities
        if self.model_type == 'classification':
            from predictor.config import CLASS_LABELS
            num_classes = len(CLASS_LABELS)

            # Create default probabilities with highest probability for the predicted class
            probabilities = np.zeros((len(predictions), num_classes))
            for i, pred in enumerate(predictions):
                # Set a high probability (0.7) for the predicted class
                probabilities[i, int(pred)] = 0.7

                # Distribute the remaining probability equally among other classes
                remaining_prob = 0.3 / (num_classes - 1)
                for j in range(num_classes):
                    if j != int(pred):
                        probabilities[i, j] = remaining_prob

            logger.info(
                f"Created default probabilities with shape {probabilities.shape}")
        else:
            # For regression, create a dummy probability array
            probabilities = np.ones((len(predictions), 1))
            logger.warning("Using dummy probabilities for regression model")

        return predictions, probabilities

    def save(self, path: Optional[str] = None) -> str:
        """
        Save the model.

        Args:
            path: Path to save the model. Defaults to MODELS_DIR/model_name.

        Returns:
            Path where the model was saved.
        """
        if path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            path = os.path.join(MODELS_DIR, f"{self.model_name}_{timestamp}")

        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)

        # Save model configuration
        config_path = os.path.join(path, "config.json")
        with open(config_path, 'w') as f:
            json.dump(self.model_config, f, indent=4)

        # Save price scaler
        price_scaler_path = os.path.join(path, "price_scaler.pkl")
        with open(price_scaler_path, 'wb') as f:
            pickle.dump(self.price_scaler, f)

        # Save model-specific data (implemented by subclasses)
        self._save_model_specific(path)

        logger.info(f"Model saved to {path}")
        return path

    @abstractmethod
    def _save_model_specific(self, path: str) -> None:
        """
        Save model-specific data.
        Must be implemented by subclasses.

        Args:
            path: Path to save the model.
        """
        pass

    @classmethod
    def load(cls, path: str) -> 'BasePredictionModel':
        """
        Load a saved model.

        Args:
            path: Path to the saved model.

        Returns:
            Loaded model instance.
        """
        # Load configuration
        config_path = os.path.join(path, "config.json")
        if not os.path.exists(config_path):
            raise FileNotFoundError(
                f"Model configuration not found at {config_path}")

        with open(config_path, 'r') as f:
            model_config = json.load(f)

        # Create model instance
        model = cls(model_config)

        # Load price scaler
        price_scaler_path = os.path.join(path, "price_scaler.pkl")
        if os.path.exists(price_scaler_path):
            with open(price_scaler_path, 'rb') as f:
                model.price_scaler = pickle.load(f)

        # Load model-specific data (implemented by subclasses)
        model._load_model_specific(path)

        return model

    @abstractmethod
    def _load_model_specific(self, path: str) -> None:
        """
        Load model-specific data.
        Must be implemented by subclasses.

        Args:
            path: Path to load the model from.
        """
        pass
