"""
LSTM model for SP500 price prediction.
Uses PyTorch for implementing a Long Short-Term Memory network.
"""

import os
import json
from typing import Dict, List, Optional, Tuple, Any

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

from predictor.models.base_model import BasePredictionModel
from predictor.config import (
    BATCH_SIZE,
    LEARNING_RATE,
    NUM_EPOCHS
)
from utils.logging_config import get_predictor_logger

# Configure logger for this module
logger = get_predictor_logger(__name__)


class LSTMNetwork(nn.Module):
    """
    LSTM neural network for sequence classification.
    """

    def __init__(
        self,
        input_size: int,
        hidden_size: int,
        num_layers: int,
        dropout: float,
        num_classes: int
    ):
        """
        Initialize the LSTM network.

        Args:
            input_size: Size of input features.
            hidden_size: Size of hidden state.
            num_layers: Number of LSTM layers.
            dropout: Dropout probability.
            num_classes: Number of output classes.
        """
        super().__init__()

        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )

        self.dropout = nn.Dropout(dropout)

        # Feed-forward layer between LSTM and output
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.relu = nn.ReLU()

        # Output layer
        self.fc2 = nn.Linear(hidden_size // 2, num_classes)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass.

        Args:
            x: Input tensor of shape (batch_size, sequence_length, input_size).

        Returns:
            Output tensor of shape (batch_size, num_classes).
        """
        # LSTM layer
        lstm_out, _ = self.lstm(x)

        # Take the output from the last time step
        lstm_out = lstm_out[:, -1, :]

        # Dropout
        lstm_out = self.dropout(lstm_out)

        # Feed-forward layer
        fc1_out = self.relu(self.fc1(lstm_out))

        # Output layer
        output = self.fc2(fc1_out)

        return output


class LSTMModel(BasePredictionModel):
    """
    LSTM model for SP500 price prediction.
    Uses PyTorch for implementing a Long Short-Term Memory network.
    """

    def __init__(self, model_config: Dict[str, Any]):
        """
        Initialize the LSTM model.

        Args:
            model_config: Dictionary containing model configuration parameters.
                Should include:
                - hidden_size: Size of LSTM hidden state
                - num_layers: Number of LSTM layers
                - dropout: Dropout probability
                - sequence_length: Length of input sequences
                - input_size: Size of input features
                - num_classes: Number of output classes
        """
        super().__init__(model_config)
        self.model_name = model_config.get('model_name', 'lstm')
        self.model_type = 'classification'

        # Set device
        self.device = torch.device(
            'cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"Using device: {self.device}")

        # Build model
        self.model = self.build_model()
        self.model.to(self.device)

    def build_model(self) -> nn.Module:
        """
        Build the LSTM model.

        Returns:
            LSTM model instance.
        """
        # Extract model parameters from config
        hidden_size = self.model_config.get('hidden_size', 64)
        num_layers = self.model_config.get('num_layers', 2)
        dropout = self.model_config.get('dropout', 0.2)
        input_size = self.model_config.get('input_size', 1)
        num_classes = self.model_config.get('num_classes', 3)

        # Create and return the model
        return LSTMNetwork(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout=dropout,
            num_classes=num_classes
        )

    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, float]:
        """
        Train the LSTM model.

        Args:
            X_train: Training features of shape (n_samples, sequence_length, input_size).
            y_train: Training labels.
            X_val: Validation features.
            y_val: Validation labels.
            output_dir: Directory to save the model.

        Returns:
            Dictionary of evaluation metrics.
        """
        logger.info(f"Training LSTM model with {X_train.shape[0]} samples")

        # Convert numpy arrays to PyTorch tensors
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
        y_train_tensor = torch.tensor(y_train, dtype=torch.long)

        # Create dataset and data loader
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(
            train_dataset,
            batch_size=BATCH_SIZE,
            shuffle=True
        )

        # Create validation data loader if validation data is provided
        if X_val is not None and y_val is not None:
            X_val_tensor = torch.tensor(X_val, dtype=torch.float32)
            y_val_tensor = torch.tensor(y_val, dtype=torch.long)
            val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
            val_loader = DataLoader(
                val_dataset,
                batch_size=BATCH_SIZE,
                shuffle=False
            )

        # Set up loss function and optimizer
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(
            self.model.parameters(),
            lr=LEARNING_RATE
        )

        # Training loop
        best_val_loss = float('inf')
        best_model_state = None

        for epoch in range(NUM_EPOCHS):
            # Training phase
            self.model.train()
            train_loss = 0.0

            for batch_X, batch_y in train_loader:
                # Move batch to device
                batch_X = batch_X.to(self.device)
                batch_y = batch_y.to(self.device)

                # Forward pass
                optimizer.zero_grad()
                outputs = self.model(batch_X)

                # Calculate loss
                loss = criterion(outputs, batch_y)

                # Backward pass
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

            avg_train_loss = train_loss / len(train_loader)
            logger.info(
                f"Epoch {epoch+1}/{NUM_EPOCHS} - Train loss: {avg_train_loss:.4f}")

            # Validation phase
            if X_val is not None and y_val is not None:
                self.model.eval()
                val_loss = 0.0
                all_preds = []
                all_labels = []

                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        # Move batch to device
                        batch_X = batch_X.to(self.device)
                        batch_y = batch_y.to(self.device)

                        # Forward pass
                        outputs = self.model(batch_X)

                        # Calculate loss
                        loss = criterion(outputs, batch_y)
                        val_loss += loss.item()

                        # Get predictions
                        _, preds = torch.max(outputs, 1)
                        all_preds.extend(preds.cpu().numpy())
                        all_labels.extend(batch_y.cpu().numpy())

                avg_val_loss = val_loss / len(val_loader)
                logger.info(
                    f"Epoch {epoch+1}/{NUM_EPOCHS} - Val loss: {avg_val_loss:.4f}")

                # Save best model
                if avg_val_loss < best_val_loss:
                    best_val_loss = avg_val_loss
                    best_model_state = self.model.state_dict().copy()
                    logger.info(
                        f"Epoch {epoch+1} - New best model saved with validation loss: {best_val_loss:.4f}")

        # Load best model
        if best_model_state is not None:
            self.model.load_state_dict(best_model_state)
            logger.info(
                f"Loaded best model with validation loss: {best_val_loss:.4f}")

        # Evaluate final model
        metrics = {}

        if X_val is not None and y_val is not None:
            self.model.eval()
            all_preds = []

            with torch.no_grad():
                X_val_tensor = torch.tensor(
                    X_val, dtype=torch.float32).to(self.device)
                outputs = self.model(X_val_tensor)
                _, preds = torch.max(outputs, 1)
                all_preds = preds.cpu().numpy()

            val_accuracy = accuracy_score(y_val, all_preds)
            val_precision = precision_score(
                y_val, all_preds, average='weighted')
            val_recall = recall_score(y_val, all_preds, average='weighted')
            val_f1 = f1_score(y_val, all_preds, average='weighted')

            metrics.update({
                'val_loss': best_val_loss,
                'val_accuracy': val_accuracy,
                'val_precision': val_precision,
                'val_recall': val_recall,
                'val_f1': val_f1
            })

            logger.info(f"Final validation accuracy: {val_accuracy:.4f}")
            logger.info(f"Final validation precision: {val_precision:.4f}")
            logger.info(f"Final validation recall: {val_recall:.4f}")
            logger.info(f"Final validation F1: {val_f1:.4f}")

            # Print classification report
            logger.info("Classification Report:")
            logger.info("\n" + classification_report(y_val, all_preds))

        # Save the model if output_dir is provided
        if output_dir:
            self.save(output_dir)

        return metrics

    def predict(
        self,
        X: np.ndarray
    ) -> np.ndarray:
        """
        Make predictions with the LSTM model.

        Args:
            X: Input features of shape (n_samples, sequence_length, input_size).

        Returns:
            Predicted class labels.
        """
        self.model.eval()

        with torch.no_grad():
            # Convert numpy array to PyTorch tensor
            X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)

            # Forward pass
            outputs = self.model(X_tensor)

            # Get predictions
            _, preds = torch.max(outputs, 1)

            return preds.cpu().numpy()

    def predict_with_confidence(
        self,
        X: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make predictions with confidence scores.

        Args:
            X: Input features of shape (n_samples, sequence_length, input_size).

        Returns:
            Tuple of (predictions, confidence_scores).
        """
        self.model.eval()

        with torch.no_grad():
            # Convert numpy array to PyTorch tensor
            X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)

            # Forward pass
            outputs = self.model(X_tensor)

            # Apply softmax to get probabilities
            probas = torch.softmax(outputs, dim=1)

            # Get predictions and confidence scores
            probs, preds = torch.max(probas, 1)

            return preds.cpu().numpy(), probs.cpu().numpy()

    def _save_model_specific(self, path: str) -> None:
        """
        Save model-specific data.

        Args:
            path: Path to save the model.
        """
        # Save model state
        model_state_path = os.path.join(path, "model_state.pt")
        torch.save(self.model.state_dict(), model_state_path)

        # Save model architecture parameters
        model_params = {
            'hidden_size': self.model_config.get('hidden_size', 64),
            'num_layers': self.model_config.get('num_layers', 2),
            'dropout': self.model_config.get('dropout', 0.2),
            'input_size': self.model_config.get('input_size', 1),
            'num_classes': self.model_config.get('num_classes', 3)
        }

        model_params_path = os.path.join(path, "model_params.json")
        with open(model_params_path, 'w') as f:
            json.dump(model_params, f, indent=4)

    def _load_model_specific(self, path: str) -> None:
        """
        Load model-specific data.

        Args:
            path: Path to load the model from.
        """
        # Load model state
        model_state_path = os.path.join(path, "model_state.pt")
        if os.path.exists(model_state_path):
            self.model.load_state_dict(torch.load(
                model_state_path, map_location=self.device))
        else:
            logger.warning(f"Model state file not found at {model_state_path}")
