"""
Model factory for creating different types of prediction models.
"""

from typing import Dict, Any, Optional

from predictor.models.base_model import BasePredictionModel
from predictor.models.logistic_regression_model import LogisticRegressionModel
from predictor.models.gradient_boosting_model import GradientBoostingModel
from predictor.models.lstm_model import LSTMModel
from utils.logging_config import get_predictor_logger

# Configure logger for this module
logger = get_predictor_logger(__name__)


def create_model(model_config: Dict[str, Any]) -> Optional[BasePredictionModel]:
    """
    Create a model based on the provided configuration.

    Args:
        model_config: Dictionary containing model configuration parameters.
            Must include a 'model_type' key with one of the following values:
            - 'logistic_regression': Create a LogisticRegressionModel
            - 'gradient_boosting': Create a GradientBoostingModel
            - 'lstm': Create an LSTMModel

    Returns:
        An instance of the specified model type, or None if the model type is not supported.
    """
    model_type = model_config.get('model_type')

    if model_type == 'logistic_regression':
        logger.info("Creating Logistic Regression model")
        return LogisticRegressionModel(model_config)

    elif model_type == 'gradient_boosting':
        logger.info("Creating Gradient Boosting model")
        return GradientBoostingModel(model_config)

    elif model_type == 'lstm':
        logger.info("Creating LSTM model")
        return LSTMModel(model_config)

    else:
        logger.error(f"Unsupported model type: {model_type}")
        return None


def load_model(model_path: str) -> Optional[BasePredictionModel]:
    """
    Load a model from the specified path.

    Args:
        model_path: Path to the saved model.

    Returns:
        Loaded model instance, or None if loading fails.
    """
    import os
    import json

    # Load model configuration to determine the model type
    config_path = os.path.join(model_path, "config.json")
    if not os.path.exists(config_path):
        logger.error(f"Model configuration not found at {config_path}")
        return None

    try:
        with open(config_path, 'r') as f:
            model_config = json.load(f)

        model_type = model_config.get('model_type')

        if model_type == 'logistic_regression':
            logger.info(f"Loading Logistic Regression model from {model_path}")
            return LogisticRegressionModel.load(model_path)

        elif model_type == 'gradient_boosting':
            logger.info(f"Loading Gradient Boosting model from {model_path}")
            return GradientBoostingModel.load(model_path)

        elif model_type == 'lstm':
            logger.info(f"Loading LSTM model from {model_path}")
            return LSTMModel.load(model_path)

        else:
            logger.error(f"Unsupported model type: {model_type}")
            return None

    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return None
