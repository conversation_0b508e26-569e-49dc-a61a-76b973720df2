"""
Gradient Boosting model for SP500 price prediction.
Uses scikit-learn's GradientBoostingClassifier for classification.
"""

import os
import pickle
from typing import Dict, List, Optional, Tuple, Any

import numpy as np
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

from predictor.models.base_model import BasePredictionModel
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__)


class GradientBoostingModel(BasePredictionModel):
    """
    Gradient Boosting model for SP500 price prediction.
    Uses scikit-learn's GradientBoostingClassifier for classification.
    """

    def __init__(self, model_config: Dict[str, Any]):
        """
        Initialize the Gradient Boosting model.

        Args:
            model_config: Dictionary containing model configuration parameters.
                Should include:
                - n_estimators: Number of boosting stages
                - learning_rate: Learning rate shrinks the contribution of each tree
                - max_depth: Maximum depth of the individual regression estimators
                - subsample: Fraction of samples to be used for fitting the individual base learners
        """
        super().__init__(model_config)
        self.model_name = model_config.get('model_name', 'gradient_boosting')
        self.model_type = 'classification'
        self.model = self.build_model()

    def build_model(self) -> GradientBoostingClassifier:
        """
        Build the Gradient Boosting model.

        Returns:
            GradientBoostingClassifier model instance.
        """
        # Extract model parameters from config
        n_estimators = self.model_config.get('n_estimators', 100)
        learning_rate = self.model_config.get('learning_rate', 0.1)
        max_depth = self.model_config.get('max_depth', 3)
        subsample = self.model_config.get('subsample', 1.0)

        # Create and return the model
        return GradientBoostingClassifier(
            n_estimators=n_estimators,
            learning_rate=learning_rate,
            max_depth=max_depth,
            subsample=subsample,
            random_state=42
        )

    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, float]:
        """
        Train the Gradient Boosting model.

        Args:
            X_train: Training features.
            y_train: Training labels.
            X_val: Validation features.
            y_val: Validation labels.
            output_dir: Directory to save the model.

        Returns:
            Dictionary of evaluation metrics.
        """
        logger.info(
            f"Training Gradient Boosting model with {X_train.shape[0]} samples")

        # Fit the model
        self.model.fit(X_train, y_train)

        # Evaluate on training data
        train_preds = self.model.predict(X_train)
        train_accuracy = accuracy_score(y_train, train_preds)
        logger.info(f"Training accuracy: {train_accuracy:.4f}")

        # Evaluate on validation data if provided
        metrics = {
            'train_accuracy': train_accuracy
        }

        if X_val is not None and y_val is not None:
            val_preds = self.model.predict(X_val)
            val_accuracy = accuracy_score(y_val, val_preds)
            val_precision = precision_score(
                y_val, val_preds, average='weighted')
            val_recall = recall_score(y_val, val_preds, average='weighted')
            val_f1 = f1_score(y_val, val_preds, average='weighted')

            metrics.update({
                'val_accuracy': val_accuracy,
                'val_precision': val_precision,
                'val_recall': val_recall,
                'val_f1': val_f1
            })

            logger.info(f"Validation accuracy: {val_accuracy:.4f}")
            logger.info(f"Validation precision: {val_precision:.4f}")
            logger.info(f"Validation recall: {val_recall:.4f}")
            logger.info(f"Validation F1: {val_f1:.4f}")

            # Print classification report
            logger.info("Classification Report:")
            logger.info("\n" + classification_report(y_val, val_preds))

            # Feature importance
            if hasattr(self.model, 'feature_importances_'):
                importances = self.model.feature_importances_
                logger.info("Feature Importances:")
                for i, importance in enumerate(importances):
                    logger.info(f"Feature {i}: {importance:.4f}")

        # Save the model if output_dir is provided
        if output_dir:
            self.save(output_dir)

        return metrics

    def predict(
        self,
        X: np.ndarray
    ) -> np.ndarray:
        """
        Make predictions with the Gradient Boosting model.

        Args:
            X: Input features.

        Returns:
            Predicted class labels.
        """
        return self.model.predict(X)

    def predict_with_confidence(
        self,
        X: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make predictions with confidence scores.

        Args:
            X: Input features.

        Returns:
            Tuple of (predictions, confidence_scores).
        """
        # Get class predictions
        predictions = self.model.predict(X)

        # Get probability estimates
        probas = self.model.predict_proba(X)
        # Use the maximum probability as the confidence score
        confidence_scores = np.max(probas, axis=1)

        return predictions, confidence_scores

    def _save_model_specific(self, path: str) -> None:
        """
        Save model-specific data.

        Args:
            path: Path to save the model.
        """
        # Save the scikit-learn model
        model_path = os.path.join(path, "model.pkl")
        with open(model_path, 'wb') as f:
            pickle.dump(self.model, f)

    def _load_model_specific(self, path: str) -> None:
        """
        Load model-specific data.

        Args:
            path: Path to load the model from.
        """
        # Load the scikit-learn model
        model_path = os.path.join(path, "model.pkl")
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                self.model = pickle.load(f)
        else:
            logger.warning(
                f"Model file not found at {model_path}, initializing new model")
            self.model = self.build_model()
