"""
Logistic Regression model for SP500 price prediction.
Uses scikit-learn's LogisticRegression for classification.
"""

import os
import pickle
from typing import Dict, List, Optional, Tuple, Any

import numpy as np
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report

from predictor.models.base_model import BasePredictionModel
from utils.logging_config import configure_logging

# Configure logger for this module
logger = configure_logging(__name__)


class LogisticRegressionModel(BasePredictionModel):
    """
    Logistic Regression model for SP500 price prediction.
    Uses scikit-learn's LogisticRegression for classification.
    """

    def __init__(self, model_config: Dict[str, Any]):
        """
        Initialize the Logistic Regression model.

        Args:
            model_config: Dictionary containing model configuration parameters.
                Should include:
                - C: Inverse of regularization strength
                - max_iter: Maximum number of iterations
                - solver: Algorithm to use in the optimization problem
                - class_weight: Weights associated with classes
        """
        super().__init__(model_config)
        self.model_name = model_config.get('model_name', 'logistic_regression')
        self.model_type = 'classification'
        self.model = self.build_model()

    def build_model(self) -> LogisticRegression:
        """
        Build the Logistic Regression model.

        Returns:
            LogisticRegression model instance.
        """
        # Extract model parameters from config
        C = self.model_config.get('C', 1.0)
        max_iter = self.model_config.get('max_iter', 1000)
        solver = self.model_config.get('solver', 'liblinear')
        class_weight = self.model_config.get('class_weight', 'balanced')

        # Create and return the model
        return LogisticRegression(
            C=C,
            max_iter=max_iter,
            solver=solver,
            class_weight=class_weight,
            multi_class='ovr',  # One-vs-rest for multi-class
            random_state=42
        )

    def train(
        self,
        X_train: np.ndarray,
        y_train: np.ndarray,
        X_val: Optional[np.ndarray] = None,
        y_val: Optional[np.ndarray] = None,
        output_dir: Optional[str] = None
    ) -> Dict[str, float]:
        """
        Train the Logistic Regression model.

        Args:
            X_train: Training features.
            y_train: Training labels.
            X_val: Validation features.
            y_val: Validation labels.
            output_dir: Directory to save the model.

        Returns:
            Dictionary of evaluation metrics.
        """
        logger.info(
            f"Training Logistic Regression model with {X_train.shape[0]} samples")

        # Fit the model
        self.model.fit(X_train, y_train)

        # Evaluate on training data
        train_preds = self.model.predict(X_train)
        train_accuracy = accuracy_score(y_train, train_preds)
        logger.info(f"Training accuracy: {train_accuracy:.4f}")

        # Evaluate on validation data if provided
        metrics = {
            'train_accuracy': train_accuracy
        }

        if X_val is not None and y_val is not None:
            val_preds = self.model.predict(X_val)
            val_accuracy = accuracy_score(y_val, val_preds)
            val_precision = precision_score(
                y_val, val_preds, average='weighted')
            val_recall = recall_score(y_val, val_preds, average='weighted')
            val_f1 = f1_score(y_val, val_preds, average='weighted')

            metrics.update({
                'val_accuracy': val_accuracy,
                'val_precision': val_precision,
                'val_recall': val_recall,
                'val_f1': val_f1
            })

            logger.info(f"Validation accuracy: {val_accuracy:.4f}")
            logger.info(f"Validation precision: {val_precision:.4f}")
            logger.info(f"Validation recall: {val_recall:.4f}")
            logger.info(f"Validation F1: {val_f1:.4f}")

            # Print classification report
            logger.info("Classification Report:")
            logger.info("\n" + classification_report(y_val, val_preds))

        # Save the model if output_dir is provided
        if output_dir:
            self.save(output_dir)

        return metrics

    def predict(
        self,
        X: np.ndarray
    ) -> np.ndarray:
        """
        Make predictions with the Logistic Regression model.

        Args:
            X: Input features.

        Returns:
            Predicted class labels.
        """
        return self.model.predict(X)

    def predict_with_confidence(
        self,
        X: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Make predictions with confidence scores and class probabilities.

        Args:
            X: Input features.

        Returns:
            Tuple of (predictions, probabilities).
            - predictions: Array of predicted class labels
            - probabilities: Array of class probabilities for each sample
        """
        # Get class predictions
        predictions = self.model.predict(X)

        # Get probability estimates
        if hasattr(self.model, 'predict_proba'):
            probabilities = self.model.predict_proba(X)
            logger.info(f"Probabilities shape: {probabilities.shape}")
            logger.info(f"Probabilities sample: {probabilities[0]}")
        else:
            # Fallback to base class implementation if predict_proba is not available
            logger.warning(
                "Model does not have predict_proba method, using default probabilities")
            _, probabilities = super().predict_with_confidence(X)

        return predictions, probabilities

    def _save_model_specific(self, path: str) -> None:
        """
        Save model-specific data.

        Args:
            path: Path to save the model.
        """
        # Save the scikit-learn model
        model_path = os.path.join(path, "model.pkl")
        with open(model_path, 'wb') as f:
            pickle.dump(self.model, f)

    def _load_model_specific(self, path: str) -> None:
        """
        Load model-specific data.

        Args:
            path: Path to load the model from.
        """
        # Load the scikit-learn model
        model_path = os.path.join(path, "model.pkl")
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                self.model = pickle.load(f)
        else:
            logger.warning(
                f"Model file not found at {model_path}, initializing new model")
            self.model = self.build_model()
