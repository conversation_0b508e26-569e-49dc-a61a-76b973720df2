"""
<PERSON><PERSON><PERSON> for making predictions using trained SP500 price prediction models.

This script loads a trained model and uses it to make predictions on new data.
It supports predicting for a specific date or a date range.
"""

import os
import sys
import json
import logging
import argparse
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from utils.logging_config import get_predictor_logger
# Import model factory
from predictor.models.model_factory import load_model

# Import dataset classes
from predictor.data.prediction_dataset import PredictionDataset

# Import configuration
from predictor.config import (
    DATE_FORMAT,
    OUTPUT_DIR,
    CLASS_LABELS
)

# Configure logging
logger = get_predictor_logger(__name__)


def parse_args():
    """
    Parse command line arguments.

    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(
        description='Make predictions using a trained SP500 price prediction model.')

    # Model arguments
    parser.add_argument('--model-path', type=str, default=None,
                        help='Path to the trained model')

    # Date arguments
    date_group = parser.add_mutually_exclusive_group(required=True)
    date_group.add_argument('--prediction-date', type=str,
                            help='Specific date to predict for (YYYY-MM-DD)')
    date_group.add_argument('--start-date', type=str,
                            help='Start date for prediction range (YYYY-MM-DD)')

    parser.add_argument('--end-date', type=str,
                        help='End date for prediction range (YYYY-MM-DD)')

    # Output arguments
    parser.add_argument('--output-file', type=str,
                        help='Path to save prediction results')
    parser.add_argument('--format', type=str, choices=['json', 'csv'], default='json',
                        help='Output format (json or csv)')

    # Additional arguments
    parser.add_argument('--include-features', action='store_true',
                        help='Include feature values in the output')
    parser.add_argument('--include-articles', action='store_true',
                        help='Include relevant news articles in the output')
    parser.add_argument('--max-articles', type=int, default=10,
                        help='Maximum number of articles to include in the output')

    return parser.parse_args()


def make_predictions(prediction_date=None, start_date=None, end_date=None,
                     model_path=None, model_name=None, ticker=None,
                     include_features=False, include_articles=True, max_articles=10,
                     output_file=None, output_format='json') -> List[Dict[str, Any]]:
    """
    Make predictions using a trained model.

    Args:
        prediction_date: Specific date to predict for (YYYY-MM-DD)
        start_date: Start date for prediction range (YYYY-MM-DD)
        end_date: End date for prediction range (YYYY-MM-DD)
        model_path: Path to the trained model
        model_name: Name of the model to use (will look in MODELS_DIR)
        ticker: Ticker symbol to predict for
        include_features: Whether to include feature values in the output
        include_articles: Whether to include relevant news articles in the output
        output_file: Path to save the prediction results (optional)
        output_format: Format to save the results in ('json' or 'csv')

    Returns:
        Dictionary with prediction results
    """
    from predictor.config import MODELS_DIR, PREDICTION_MODEL_NAME

    # Handle command-line args object if passed instead of individual parameters
    if isinstance(prediction_date, argparse.Namespace):
        args = prediction_date
        prediction_date = args.prediction_date
        start_date = args.start_date
        end_date = args.end_date
        model_path = args.model_path
        include_features = args.include_features
        include_articles = args.include_articles
        output_file = args.output_file
        output_format = args.format
        max_articles = args.max_articles
        model_name = None
        ticker = None

    # Determine model path
    if model_path is None:
        if model_name is None:
            model_name = PREDICTION_MODEL_NAME
        model_path = os.path.join(MODELS_DIR, model_name)
        logger.info(f"Using model: {model_name} at path: {model_path}")

    # Load model
    logger.info(f"Loading model from {model_path}")
    model = load_model(model_path)

    if model is None:
        error_msg = f"Failed to load model from {model_path}"
        logger.error(error_msg)
        return {"error": error_msg}

    # Load model configuration
    try:
        with open(os.path.join(model_path, "config.json"), 'r') as f:
            model_config = json.load(f)
    except Exception as e:
        error_msg = f"Failed to load model configuration: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}

    # Determine date range for prediction
    if prediction_date:
        if datetime.strptime(prediction_date, DATE_FORMAT) > datetime.now():
            prediction_date = datetime.now().strftime(DATE_FORMAT)
        start_date = prediction_date
        end_date = prediction_date
    else:
        if not start_date:
            error_msg = "Either prediction_date or start_date must be provided"
            logger.error(error_msg)
            return {"error": error_msg}
        end_date = end_date or datetime.now().strftime(DATE_FORMAT)

    # Create dataset configuration
    dataset_config = model_config['dataset_config']

    # Override ticker if provided
    if ticker:
        dataset_config['ticker'] = ticker

    # Create prediction dataset
    logger.info(f"Creating prediction dataset for {start_date} to {end_date}")
    dataset = PredictionDataset.from_date_range(
        start_date=start_date,
        end_date=end_date,
        dataset_config=dataset_config,
        is_training=False
    )

    # Prepare features for prediction
    logger.info("Preparing features for prediction")
    features = dataset.prepare_features(is_training=False)

    X = features.get('X')
    dates = features.get('dates')
    feature_names = features.get('feature_names')
    logger.info(f"Features: {feature_names}")

    if X.size == 0:
        logger.error("No valid features found for prediction")
        return

    # Make predictions with confidence
    logger.info(f"Making predictions for {len(dates)} dates")
    predictions, probabilities = model.predict_with_confidence(X)
    logger.info(
        f"Predictions shape: {predictions.shape}, Probabilities shape: {probabilities.shape}")

    # Prepare results
    results = []
    for i, date in enumerate(dates):
        # Convert prediction to int and get the label
        pred_int = int(predictions[i])
        pred_label = CLASS_LABELS.get(pred_int, 'unknown')

        result = {
            'date': date,
            'prediction_date': date,  # Add prediction_date for consistency
            'prediction': pred_label,  # Use the label as the prediction
            'prediction_label': pred_label,
            'prediction_class': pred_int  # Keep the numeric class for reference
        }

        # Add probabilities if available
        if probabilities is not None:
            probs = probabilities[i]
            # Create a dictionary mapping class labels to probabilities
            prob_dict = {
                CLASS_LABELS.get(j, 'unknown'): float(prob)
                for j, prob in enumerate(probs)
            }
            result['probabilities'] = prob_dict

            # Add overall confidence (highest probability)
            result['confidence'] = float(max(probs))
            logger.info(f"Confidence for date {date}: {result['confidence']}")

        # Add features if requested
        if include_features and feature_names is not None:
            result['features'] = {
                name: float(X[i, j])
                for j, name in enumerate(feature_names)
            }

        # Add relevant articles if requested
        if include_articles:
            # Get articles for this date
            articles_per_day = dataset.news_dataset.get_articles_for_date_range(
                date, 3)
            logger.info(
                f"Found {sum(len(x) for x in articles_per_day)} articles for past 3 days of date {date}")

            relevant_articles = []
            for articles in articles_per_day:
                # Include all articles with their sentiment information
                for article in articles:
                    indicator_analysis = article['indicator_analysis']
                    indicator_score = indicator_analysis['scores']['positive'] - \
                        indicator_analysis['scores']['negative']
                    indicator_analysis['score'] = indicator_score

                    relevant_articles.append({
                        'title': article['title'],
                        'url': article['url'],
                        'source': article['source'],
                        'publish_time': article.get('publish_time', ''),
                        'summary': article.get('summary', ''),
                        'indicator_analysis': indicator_analysis
                    })

            # Sort articles by influence score
            relevant_articles = sorted(relevant_articles, key=lambda x: abs(
                x['indicator_analysis']['score']), reverse=True)

            # Limit to top 10 articles
            result['relevant_articles'] = relevant_articles[:max_articles]

        results.append(result)

    # Save results if output file is specified
    if output_file:
        try:
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            if output_format == 'json':
                with open(output_file, 'w') as f:
                    json.dump(results, f, indent=2)
            elif output_format == 'csv':
                # Convert results to DataFrame
                df = pd.DataFrame(results)
                # Flatten nested dictionaries
                if 'probabilities' in df.columns:
                    prob_df = pd.json_normalize(df['probabilities'])
                    df = pd.concat(
                        [df.drop('probabilities', axis=1), prob_df], axis=1)
                if 'features' in df.columns:
                    feat_df = pd.json_normalize(df['features'])
                    df = pd.concat(
                        [df.drop('features', axis=1), feat_df], axis=1)
                # Drop articles column as it's not suitable for CSV
                if 'relevant_articles' in df.columns:
                    df = df.drop(columns=['relevant_articles'])
                # Save to CSV
                df.to_csv(output_file, index=False)
            logger.info(
                f"Results saved to {output_file} in {output_format} format")
        except Exception as e:
            logger.error(f"Failed to save results: {str(e)}")

    # Print summary
    logger.info("Prediction summary:")
    for label in CLASS_LABELS.values():
        count = sum(
            1 for result in results if result['prediction_label'] == label)
        if results:
            percentage = count/len(results)*100
            logger.info(f"  {label}: {count} ({percentage:.1f}%)")
        else:
            logger.info(f"  {label}: {count} (0.0%)")

    logger.info(f"Returning results: {json.dumps(results, indent=2)}")
    return results


if __name__ == "__main__":
    args = parse_args()
    make_predictions(args)
