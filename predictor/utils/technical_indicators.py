#!/usr/bin/env python
"""
Technical Indicators Utility Module

This module provides utility functions for calculating technical indicators
from price data, for use in SP500 price prediction models and technical analysis.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any

# Configure logger for this module
logger = logging.getLogger(__name__)


def calculate_sma(
    data: pd.DataFrame,
    periods: List[int] = [5, 10, 20],
    price_col: str = 'Close',
    include_distance: bool = False
) -> pd.DataFrame:
    """
    Calculate Simple Moving Averages (SMA) for the given periods.

    Args:
        data: DataFrame containing price data.
        periods: List of periods to calculate SMA for.
        price_col: Column name for price data.
        include_distance: Whether to include distance from price to SMA.

    Returns:
        DataFrame with SMA columns added.
    """
    result = data.copy()

    for period in periods:
        # Calculate SMA
        result[f'sma_{period}'] = result[price_col].rolling(
            window=period).mean()

        # Calculate distance from price to SMA as percentage
        if include_distance:
            close_values = result[price_col].values
            sma_values = result[f'sma_{period}'].values
            result[f'sma_{period}_dist'] = pd.Series(
                ((close_values - sma_values) / sma_values * 100),
                index=result.index
            )

    return result


def calculate_ema(
    data: pd.DataFrame,
    periods: List[int] = [5, 10, 20],
    price_col: str = 'Close',
    include_distance: bool = True
) -> pd.DataFrame:
    """
    Calculate Exponential Moving Averages (EMA) for the given periods.

    Args:
        data: DataFrame containing price data.
        periods: List of periods to calculate EMA for.
        price_col: Column name for price data.
        include_distance: Whether to include distance from price to EMA.

    Returns:
        DataFrame with EMA columns added.
    """
    result = data.copy()

    for period in periods:
        # Calculate EMA
        result[f'ema_{period}'] = result[price_col].ewm(
            span=period, adjust=False).mean()

        # Calculate distance from price to EMA as percentage
        if include_distance:
            close_values = result[price_col].values
            ema_values = result[f'ema_{period}'].values
            result[f'ema_{period}_dist'] = pd.Series(
                ((close_values - ema_values) / ema_values * 100),
                index=result.index
            )

    return result


def calculate_rsi(
    data: pd.DataFrame,
    periods: List[int] = [14],
    price_col: str = 'Close'
) -> pd.DataFrame:
    """
    Calculate Relative Strength Index (RSI) for the given periods.

    Args:
        data: DataFrame containing price data.
        periods: List of periods to calculate RSI for.
        price_col: Column name for price data.

    Returns:
        DataFrame with RSI columns added.
    """
    result = data.copy()

    for period in periods:
        delta = result[price_col].diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
        rs = gain / loss
        result[f'rsi_{period}'] = 100 - (100 / (1 + rs))

    return result


def calculate_macd(
    data: pd.DataFrame,
    fast_period: int = 12,
    slow_period: int = 26,
    signal_period: int = 9,
    price_col: str = 'Close'
) -> pd.DataFrame:
    """
    Calculate Moving Average Convergence Divergence (MACD).

    Args:
        data: DataFrame containing price data.
        fast_period: Period for fast EMA.
        slow_period: Period for slow EMA.
        signal_period: Period for signal line.
        price_col: Column name for price data.

    Returns:
        DataFrame with MACD columns added.
    """
    result = data.copy()

    # Calculate MACD
    ema_fast = result[price_col].ewm(span=fast_period, adjust=False).mean()
    ema_slow = result[price_col].ewm(span=slow_period, adjust=False).mean()
    result['macd'] = ema_fast - ema_slow
    result['macd_signal'] = result['macd'].ewm(
        span=signal_period, adjust=False).mean()
    result['macd_hist'] = result['macd'] - result['macd_signal']

    return result


def calculate_bollinger_bands(
    data: pd.DataFrame,
    periods: List[int] = [20],
    num_std: float = 2.0,
    price_col: str = 'Close'
) -> pd.DataFrame:
    """
    Calculate Bollinger Bands for the given periods.

    Args:
        data: DataFrame containing price data.
        periods: List of periods to calculate Bollinger Bands for.
        num_std: Number of standard deviations for bands.
        price_col: Column name for price data.

    Returns:
        DataFrame with Bollinger Bands columns added.
    """
    result = data.copy()

    for period in periods:
        # Calculate middle band (SMA)
        result[f'bb_middle_{period}'] = result[price_col].rolling(
            window=period).mean()

        # Calculate standard deviation
        result[f'bb_std_{period}'] = result[price_col].rolling(
            window=period).std()

        # Calculate upper and lower bands
        result[f'bb_upper_{period}'] = result[f'bb_middle_{period}'] + \
            num_std * result[f'bb_std_{period}']
        result[f'bb_lower_{period}'] = result[f'bb_middle_{period}'] - \
            num_std * result[f'bb_std_{period}']

        # Calculate BB width and %B
        result[f'bb_width_{period}'] = (
            result[f'bb_upper_{period}'] - result[f'bb_lower_{period}']) / result[f'bb_middle_{period}']
        result[f'bb_pct_b_{period}'] = (result[price_col] - result[f'bb_lower_{period}']) / (
            result[f'bb_upper_{period}'] - result[f'bb_lower_{period}'])

    return result


def calculate_stochastic(
    data: pd.DataFrame,
    periods: List[int] = [14],
    k_smoothing: int = 3,
    price_col: str = 'Close'
) -> pd.DataFrame:
    """
    Calculate Stochastic Oscillator for the given periods.

    Args:
        data: DataFrame containing price data.
        periods: List of periods to calculate Stochastic for.
        k_smoothing: Smoothing period for %K.
        price_col: Column name for price data.

    Returns:
        DataFrame with Stochastic columns added.
    """
    result = data.copy()

    for period in periods:
        # Calculate %K
        result[f'stoch_k_{period}'] = 100 * ((result[price_col] - result['Low'].rolling(window=period).min()) /
                                             (result['High'].rolling(window=period).max() - result['Low'].rolling(window=period).min()))

        # Calculate %D (3-day SMA of %K)
        result[f'stoch_d_{period}'] = result[f'stoch_k_{period}'].rolling(
            window=k_smoothing).mean()

    return result


def calculate_atr(
    data: pd.DataFrame,
    periods: List[int] = [14],
    include_percentage: bool = True
) -> pd.DataFrame:
    """
    Calculate Average True Range (ATR) for the given periods.

    Args:
        data: DataFrame containing price data.
        periods: List of periods to calculate ATR for.
        include_percentage: Whether to include ATR as percentage of price.

    Returns:
        DataFrame with ATR columns added.
    """
    result = data.copy()

    for period in periods:
        # Calculate true range
        high_low = result['High'] - result['Low']
        high_close = (result['High'] - result['Close'].shift()).abs()
        low_close = (result['Low'] - result['Close'].shift()).abs()
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = ranges.max(axis=1)

        # Calculate ATR
        result[f'atr_{period}'] = true_range.rolling(window=period).mean()

        # Calculate ATR as percentage of price
        if include_percentage:
            result[f'atr_pct_{period}'] = result[f'atr_{period}'] / \
                result['Close'] * 100

    return result


def calculate_obv(
    data: pd.DataFrame,
    normalize_period: Optional[int] = 20
) -> pd.DataFrame:
    """
    Calculate On-Balance Volume (OBV).

    Args:
        data: DataFrame containing price data.
        normalize_period: Period for normalizing OBV. If None, no normalization is done.

    Returns:
        DataFrame with OBV columns added.
    """
    result = data.copy()

    # Calculate OBV
    obv = (np.sign(result['Close'].diff()) *
           result['Volume']).fillna(0).cumsum()
    result['obv'] = obv

    # Normalize OBV if requested
    if normalize_period is not None:
        result['obv_norm'] = (result['obv'] - result['obv'].rolling(window=normalize_period).min()) / (
            result['obv'].rolling(window=normalize_period).max() - result['obv'].rolling(window=normalize_period).min())

    return result


def calculate_momentum(
    data: pd.DataFrame,
    periods: List[int] = [10, 20],
    price_col: str = 'Close'
) -> pd.DataFrame:
    """
    Calculate Momentum (price change over period) for the given periods.

    Args:
        data: DataFrame containing price data.
        periods: List of periods to calculate Momentum for.
        price_col: Column name for price data.

    Returns:
        DataFrame with Momentum columns added.
    """
    result = data.copy()

    for period in periods:
        result[f'momentum_{period}'] = result[price_col].pct_change(
            periods=period, fill_method=None) * 100

    return result


def calculate_all_indicators(
    data: pd.DataFrame,
    indicators: List[str] = None,
    price_col: str = 'Close'
) -> pd.DataFrame:
    """
    Calculate all specified technical indicators.

    Args:
        data: DataFrame containing price data.
        indicators: List of indicators to calculate. If None, calculates all.
        price_col: Column name for price data.

    Returns:
        DataFrame with all indicator columns added.
    """
    if data.empty:
        logger.warning("Empty DataFrame provided, cannot calculate indicators")
        return data

    # Default to all indicators if none specified
    if indicators is None:
        indicators = [
            'sma', 'ema', 'rsi', 'macd', 'bollinger_bands',
            'stochastic', 'atr', 'obv', 'momentum'
        ]

    # Ensure we have a copy to avoid modifying the original
    result = data.copy()

    # Ensure the DataFrame has the expected structure
    # The Yahoo Finance API should ensure this, but we double-check here
    # to make the utility functions more robust when used with other data sources
    if not isinstance(result.index, pd.DatetimeIndex):
        try:
            result.index = pd.to_datetime(result.index)
        except:
            logger.warning("Could not convert index to DatetimeIndex")

    # Ensure required columns exist and are numeric
    required_cols = [price_col]
    if 'stochastic' in indicators or 'atr' in indicators:
        required_cols.extend(['High', 'Low'])
    if 'obv' in indicators:
        required_cols.append('Volume')

    for col in required_cols:
        if col not in result.columns:
            logger.warning(f"Required column '{col}' not found in data")
            return data
        result[col] = pd.to_numeric(result[col], errors='coerce')

    # Calculate each indicator
    if 'sma' in indicators:
        result = calculate_sma(result, price_col=price_col)

    if 'ema' in indicators:
        result = calculate_ema(result, price_col=price_col)

    if 'rsi' in indicators:
        result = calculate_rsi(result, price_col=price_col)

    if 'macd' in indicators:
        result = calculate_macd(result, price_col=price_col)

    if 'bollinger_bands' in indicators:
        result = calculate_bollinger_bands(result, price_col=price_col)

    if 'stochastic' in indicators:
        result = calculate_stochastic(result, price_col=price_col)

    if 'atr' in indicators:
        result = calculate_atr(result)

    if 'obv' in indicators:
        result = calculate_obv(result)

    if 'momentum' in indicators:
        result = calculate_momentum(result, price_col=price_col)

    return result
