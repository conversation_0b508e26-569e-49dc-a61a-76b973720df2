"""
Utility functions for sampling datasets.

This module provides shared functionality for applying various sampling strategies
to datasets, used by both training and analysis scripts.
"""

import logging
from typing import Dict, List, Optional, Any

# Import sampling functions
from predictor.data.data_sampler import (
    sample_by_class_ratio,
    sample_by_date,
    sample_stratified_by_source,
    sample_by_sentiment_score
)

# Configure logger for this module
logger = logging.getLogger(__name__)


def apply_sampling_strategies(
    samples: List[Dict],
    args: Any,
    logger: Optional[logging.Logger] = None
) -> List[Dict]:
    """
    Apply sampling strategies to a list of samples based on command line arguments.

    This function is used by both training and analysis scripts to ensure
    consistent application of sampling strategies.

    Args:
        samples: The original list of samples to sample from
        args: Command line arguments containing sampling parameters
        logger: Optional logger instance to use for logging

    Returns:
        A new list of samples with sampling strategies applied
    """
    # Use provided logger or default to module logger
    log = logger or logging.getLogger(__name__)

    # If no sampling strategy is specified, return the original samples
    if args.sampling_strategy == "none":
        return samples

    log.info(f"Applying sampling strategy: {args.sampling_strategy}")

    # Make a copy of the samples to avoid modifying the original
    sampled_samples = samples

    # Apply by_ratio first (moved to top as requested)
    if args.sampling_strategy == "by_ratio" or args.sampling_strategy == "combined":
        # Apply ratio-based sampling
        log.info("Applying ratio-based sampling")
        # Create class ratios dictionary
        class_ratios = {
            0: args.positive_ratio,  # Positive class
            1: args.negative_ratio,  # Negative class
            2: args.neutral_ratio    # Neutral class
        }
        sampled_samples = sample_by_class_ratio(
            sampled_samples,
            class_ratios,
            upsample_minority_classes=getattr(
                args, 'upsample_minority_classes', False)
        )

    # Apply other sampling strategies
    if args.sampling_strategy == "by_date" or args.sampling_strategy == "combined":
        # Apply date-based sampling
        log.info("Applying date-based sampling")
        sampled_samples = sample_by_date(
            sampled_samples,
            args.max_samples_per_date,
            keep_positive_negative=args.keep_positive_negative
        )

    if args.sampling_strategy == "by_source" or args.sampling_strategy == "combined":
        # Apply source-based sampling
        log.info("Applying source-based sampling")
        sampled_samples = sample_stratified_by_source(
            sampled_samples,
            args.max_samples_per_source,
            keep_positive_negative=args.keep_positive_negative
        )

    if args.sampling_strategy == "by_sentiment_score" or args.sampling_strategy == "combined":
        # Apply sentiment score-based sampling
        log.info("Applying sentiment score-based sampling")
        sampled_samples = sample_by_sentiment_score(
            sampled_samples,
            args.sentiment_score_threshold,
            keep_positive_negative=args.keep_positive_negative
        )

    # Log sampling strategy details
    if args.keep_positive_negative and args.sampling_strategy != "by_ratio":
        log.info(
            "Kept all positive and negative samples for non-ratio sampling strategies")

    if args.sampling_strategy == "by_ratio" or args.sampling_strategy == "combined":
        log.info(
            f"Class ratios: positive={args.positive_ratio:.2f}, negative={args.negative_ratio:.2f}, neutral={args.neutral_ratio:.2f}")
        if getattr(args, 'upsample_minority_classes', False):
            log.info("Upsampling minority classes to match target ratios")

    if args.sampling_strategy == "by_date" or args.sampling_strategy == "combined":
        log.info(f"Max samples per date: {args.max_samples_per_date}")

    if args.sampling_strategy == "by_source" or args.sampling_strategy == "combined":
        log.info(
            f"Max samples per source: {args.max_samples_per_source or 'auto'}")

    if args.sampling_strategy == "by_sentiment_score" or args.sampling_strategy == "combined":
        log.info(
            f"Sentiment score threshold: {args.sentiment_score_threshold}")

    return sampled_samples


def get_sampling_summary(args: Any) -> Dict[str, str]:
    """
    Generate a summary of the sampling strategies applied.

    Args:
        args: Command line arguments containing sampling parameters

    Returns:
        Dictionary with sampling summary information for display
    """
    summary = {}

    if args.sampling_strategy == "none":
        return summary

    summary["strategy"] = args.sampling_strategy

    if args.keep_positive_negative and args.sampling_strategy != "by_ratio":
        if args.sampling_strategy == "combined":
            summary["keep_positive_negative"] = "Yes (for date, source, and sentiment strategies only, not for ratio)"
        else:
            summary["keep_positive_negative"] = "Yes"

    if args.sampling_strategy == "by_ratio" or args.sampling_strategy == "combined":
        summary["class_ratios"] = f"positive={args.positive_ratio:.2f}, negative={args.negative_ratio:.2f}, neutral={args.neutral_ratio:.2f}"
        if getattr(args, 'upsample_minority_classes', False):
            summary["upsample_minority_classes"] = "Yes"

    if args.sampling_strategy == "by_date" or args.sampling_strategy == "combined":
        summary["max_samples_per_date"] = str(args.max_samples_per_date)

    if args.sampling_strategy == "by_source" or args.sampling_strategy == "combined":
        summary["max_samples_per_source"] = str(
            args.max_samples_per_source or 'auto')

    if args.sampling_strategy == "by_sentiment_score" or args.sampling_strategy == "combined":
        summary["sentiment_score_threshold"] = str(
            args.sentiment_score_threshold)

    return summary
