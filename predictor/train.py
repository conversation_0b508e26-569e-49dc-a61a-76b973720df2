"""
Script for training SP500 price prediction models.
Supports multiple model types with a consistent interface.

This module provides functionality for:
1. Training different types of prediction models (Logistic Regression, Gradient Boosting, LSTM)
2. Loading and processing news and price data
3. Extracting features for model training
4. Evaluating model performance
5. Saving trained models for later use
6. Analyzing and visualizing feature importance

Feature importance analysis is supported for tree-based models (Gradient Boosting) and
linear models (Logistic Regression), but not for deep learning models (LSTM).
"""

import os
import json
import argparse
from datetime import datetime
from typing import Dict, Any, Optional, List

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

from predictor.data.prediction_dataset import PredictionDataset
from predictor.models.model_factory import create_model
from predictor.config import (
    MODELS_DIR,
    DATE_FORMAT,
    PREDICTION_DATASET_CONFIG,
    SP500_TICKER,
    TRAIN_TEST_SPLIT,
    VALIDATION_SPLIT,
    ARTICLE_FEATURE_DAYS,
    PRICE_FEATURE_DAYS,
    CLASS_LABELS
)
from utils.logging_config import get_predictor_logger

# Configure logger for this module
logger = get_predictor_logger(__name__)


def convert_numpy_to_python(obj):
    """
    Convert NumPy types to Python native types for JSON serialization.

    Args:
        obj: Object that may contain NumPy types

    Returns:
        Object with NumPy types converted to Python native types
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, dict):
        return {k: convert_numpy_to_python(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_to_python(item) for item in obj]
    else:
        return obj


def analyze_feature_importance(
    model: Any,
    feature_names: List[str],
    output_dir: str,
    top_n: int = 20,
    export_csv: bool = True
) -> Dict[str, Any]:
    """
    Analyze and visualize feature importance from the trained model.

    Args:
        model: Trained model instance
        feature_names: List of feature names
        output_dir: Directory to save visualizations and exports
        top_n: Number of top features to display in visualization
        export_csv: Whether to export feature importance to CSV

    Returns:
        Dictionary containing feature importance information
    """
    logger.info(
        f"Analyzing feature importance for {len(feature_names)} features")

    # Create plots directory
    plots_dir = os.path.join(output_dir, "plots")
    os.makedirs(plots_dir, exist_ok=True)

    # Initialize feature importance dictionary
    feature_importance = {}
    importance_values = None

    # Extract feature importance based on model type
    if hasattr(model, 'feature_importances_'):
        # For tree-based models like GradientBoostingClassifier
        importance_values = model.feature_importances_
        logger.info("Extracted feature importance from tree-based model")
    elif hasattr(model, 'coef_'):
        # For linear models like LogisticRegression
        # For multi-class, take the average absolute coefficient across all classes
        if len(model.coef_.shape) > 1:
            importance_values = np.mean(np.abs(model.coef_), axis=0)
        else:
            importance_values = np.abs(model.coef_)
        logger.info(
            "Extracted feature importance from linear model coefficients")
    else:
        logger.warning("Model does not provide feature importance information")
        return {"error": "Model does not provide feature importance information"}

    # Create feature importance dictionary
    if importance_values is not None and len(importance_values) == len(feature_names):
        for name, importance in zip(feature_names, importance_values):
            feature_importance[name] = float(importance)

        # Log feature importance
        logger.info("Feature Importance:")
        for name, importance in sorted(feature_importance.items(), key=lambda x: x[1], reverse=True):
            logger.info(f"{name}: {importance:.6f}")

        # Create DataFrame for visualization and export
        importance_df = pd.DataFrame({
            'Feature': feature_names,
            'Importance': importance_values
        })

        # Sort by importance (descending)
        importance_df = importance_df.sort_values(
            'Importance', ascending=False)

        # Visualize top N features
        plt.figure(figsize=(12, 8))

        # Plot only top N features
        top_features = importance_df.head(top_n)

        # Create horizontal bar chart
        plt.barh(top_features['Feature'], top_features['Importance'])

        # Add importance values as text
        for i, importance in enumerate(top_features['Importance']):
            plt.text(importance + 0.001, i, f"{importance:.4f}", va='center')

        plt.xlabel('Importance')
        plt.ylabel('Feature')
        plt.title(f'Top {top_n} Feature Importance')
        plt.tight_layout()

        # Save visualization
        viz_path = os.path.join(plots_dir, "feature_importance.png")
        plt.savefig(viz_path, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"Feature importance visualization saved to {viz_path}")

        # Export to CSV if requested
        if export_csv:
            csv_path = os.path.join(output_dir, "feature_importance.csv")
            importance_df.to_csv(csv_path, index=False)
            logger.info(f"Feature importance data exported to {csv_path}")

        return {
            "feature_importance": feature_importance,
            "visualization_path": viz_path,
            "export_path": csv_path if export_csv else None
        }
    else:
        logger.error(
            f"Feature importance shape mismatch: {len(importance_values)} values for {len(feature_names)} features")
        return {"error": "Feature importance shape mismatch"}


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Train SP500 price prediction model")

    # Model arguments
    parser.add_argument("--model_type", type=str, required=True,
                        choices=["logistic_regression",
                                 "gradient_boosting", "lstm"],
                        help="Type of model to train")
    parser.add_argument("--model_name", type=str, default=None,
                        help="Name for the trained model")
    parser.add_argument("--config_file", type=str, default=None,
                        help="Path to model configuration file (JSON)")
    # Dataset arguments
    parser.add_argument("--start_date", type=str, required=True,
                        help="Start date for training data (YYYY-MM-DD)")
    parser.add_argument("--end_date", type=str, required=True,
                        help="End date for training data (YYYY-MM-DD)")

    # Data export options
    parser.add_argument("--export_data", action="store_true",
                        help="Export training data to a file")
    parser.add_argument("--export_format", type=str, choices=["json", "csv"], default="json",
                        help="Format for exporting training data (default: json)")

    # Feature importance analysis options
    parser.add_argument("--feature_importance", action="store_true",
                        help="Analyze and visualize feature importance after training")
    parser.add_argument("--top_n_features", type=int, default=20,
                        help="Number of top features to display in importance visualization")

    # Output arguments
    parser.add_argument("--output_dir", type=str, default=None,
                        help="Directory to save the trained model")

    return parser.parse_args()


def load_config_file(config_file: str) -> Dict[str, Any]:
    """
    Load model configuration from a JSON file.

    Args:
        config_file: Path to the configuration file.

    Returns:
        Dictionary containing model configuration.
    """
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        logger.info(f"Loaded configuration from {config_file}")
        return config
    except Exception as e:
        logger.error(f"Error loading configuration file: {e}")
        return {}


def create_model_config(args, config_file_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Create model configuration from command line arguments and config file.

    Args:
        args: Command line arguments.
        config_file_data: Configuration data loaded from a file.

    Returns:
        Dictionary containing model configuration.
    """
    # Start with config file data if provided
    model_config = config_file_data.copy() if config_file_data else {}

    # Set model type and name
    model_config['model_type'] = args.model_type
    model_config['model_name'] = args.model_name or f"prediction_model_{args.model_type}"

    # Set prediction dataset configuration
    model_config['dataset_config'] = model_config.get(
        'dataset_config', PREDICTION_DATASET_CONFIG)
    dataset_config = model_config['dataset_config']
    feature_config = dataset_config.get('feature_config', {})
    price_config = feature_config.get('price_config', {})

    # Set sequence format for LSTM models
    if args.model_type == 'lstm':
        model_config['input_size'] = 1  # Default for price features
        model_config['num_classes'] = 3  # Three-class classification

    return model_config


def export_training_data(X, y, feature_names, dates, output_dir, export_format="json", metadata=None, label_list=None):
    """
    Export training data to a file for inspection.

    Args:
        X: NumPy array of features, where each row corresponds to a sample.
        y: NumPy array of labels.
        feature_names: List of feature names corresponding to columns in X.
        dates: List of dates corresponding to each sample.
        output_dir: Directory to save the exported data.
        export_format: Format to export the data in ('json', 'csv', or 'parquet').
        metadata: Dictionary with metadata about the features and preprocessing.
        label_list: Optional list of raw label dictionaries (for backward compatibility).
    """
    if X is None or X.size == 0 or y is None or y.size == 0:
        logger.warning("No features or labels to export")
        return

    # Create data directory
    data_dir = os.path.join(output_dir, "data")
    os.makedirs(data_dir, exist_ok=True)

    # Prepare export data
    export_data = []

    # Process each sample
    for i in range(len(X)):
        # Get basic information
        date = dates[i] if i < len(dates) else f"sample_{i}"
        label_value = int(y[i])

        # Get label name from metadata if available
        label_name = "unknown"
        if metadata and 'class_labels' in metadata:
            label_name = metadata['class_labels'].get(
                label_value, "unknown")

        # Create a record for this sample
        record = {
            'date': date,
            'label': label_value,
            'label_name': label_name,
            'features': {}
        }

        # Add return value if available from label_list
        if label_list and i < len(label_list) and label_list[i]:
            record['return_value'] = label_list[i].get('return_value', 0.0)

        # Add features
        for j, feature_name in enumerate(feature_names):
            if j < X.shape[1]:
                record['features'][feature_name] = float(X[i, j])

        export_data.append(record)

    # Add metadata to export
    export_metadata = {
        'feature_count': len(feature_names),
        'sample_count': len(export_data),
        'feature_names': feature_names,
        'export_date': datetime.now().isoformat(),
        'class_distribution': {int(k): int(v) for k, v in zip(*np.unique(y, return_counts=True))}
    }

    # Add additional metadata if provided
    if metadata:
        export_metadata.update(metadata)

    # Export data in the requested format
    if export_format == "json":
        # Save as JSON
        json_path = os.path.join(data_dir, "training_data.json")

        # Create export package with data and metadata
        export_package = {
            'metadata': export_metadata,
            'data': export_data
        }

        # Convert all NumPy types to Python native types
        export_package_converted = convert_numpy_to_python(export_package)

        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(export_package_converted, f,
                      indent=2, ensure_ascii=False)

        logger.info(
            f"Exported {len(export_data)} training samples to {json_path}")

    elif export_format == "csv":
        # Save as CSV
        import csv

        # Save data
        csv_path = os.path.join(data_dir, "training_data.csv")
        with open(csv_path, 'w', encoding='utf-8', newline='') as f:
            # Define basic CSV fields
            fieldnames = ['date', 'label', 'label_name']

            # Add return_value if available
            if export_data and 'return_value' in export_data[0]:
                fieldnames.append('return_value')

            # Add feature fields
            if export_data and 'features' in export_data[0]:
                for feature_name in feature_names:
                    fieldnames.append(feature_name)

            # Create CSV writer
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            # Write data
            for record in export_data:
                # Start with basic fields
                row = {
                    'date': record.get('date', ''),
                    'label': record.get('label', 2),
                    'label_name': record.get('label_name', 'neutral')
                }

                # Add return value if available
                if 'return_value' in record:
                    row['return_value'] = record['return_value']

                # Add all features
                if 'features' in record:
                    for feature_name in feature_names:
                        if feature_name in record['features']:
                            row[feature_name] = record['features'][feature_name]
                        else:
                            row[feature_name] = 0.0

                writer.writerow(row)

        # Save metadata separately
        metadata_path = os.path.join(data_dir, "metadata.json")
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(convert_numpy_to_python(export_metadata),
                      f, indent=2, ensure_ascii=False)

        logger.info(
            f"Exported {len(export_data)} training samples to {csv_path}")
        logger.info(f"Exported metadata to {metadata_path}")

    elif export_format == "parquet":
        try:
            import pandas as pd

            # Create DataFrame
            df_data = []
            for record in export_data:
                row = {
                    'date': record.get('date', ''),
                    'label': record.get('label', 2),
                    'label_name': record.get('label_name', 'neutral')
                }

                # Add return value if available
                if 'return_value' in record:
                    row['return_value'] = record['return_value']

                # Add features
                if 'features' in record:
                    for feature_name in feature_names:
                        if feature_name in record['features']:
                            row[feature_name] = record['features'][feature_name]
                        else:
                            row[feature_name] = 0.0

                df_data.append(row)

            # Create DataFrame
            df = pd.DataFrame(df_data)

            # Save as Parquet
            parquet_path = os.path.join(data_dir, "training_data.parquet")
            df.to_parquet(parquet_path, index=False)

            # Save metadata separately
            metadata_path = os.path.join(data_dir, "metadata.json")
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(convert_numpy_to_python(export_metadata),
                          f, indent=2, ensure_ascii=False)

            logger.info(
                f"Exported {len(export_data)} training samples to {parquet_path}")
            logger.info(f"Exported metadata to {metadata_path}")

        except ImportError:
            logger.error(
                "pandas is required for parquet export. Falling back to JSON.")
            # Fall back to JSON
            export_training_data(X, y, feature_names, dates, output_dir,
                                 "json", metadata, label_list)

    else:
        logger.error(
            f"Unsupported export format: {export_format}. Supported formats: json, csv, parquet")
        return


def train_model(args):
    """
    Train a model with the specified configuration.

    Args:
        args: Command line arguments.
    """
    # Load configuration from file if provided
    config_file_data = {}
    if args.config_file:
        config_file_data = load_config_file(args.config_file)

    # Create model configuration
    model_config = create_model_config(args, config_file_data)

    # Create output directory
    if args.output_dir:
        output_dir = args.output_dir
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(
            MODELS_DIR, f"{model_config['model_name']}_{timestamp}")

    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"Output directory: {output_dir}")

    # Save configuration
    config_path = os.path.join(output_dir, "config.json")
    with open(config_path, 'w') as f:
        json.dump(model_config, f, indent=4)

    # Create prediction dataset
    logger.info(
        f"Creating prediction dataset for {args.start_date} to {args.end_date}")
    dataset = PredictionDataset.from_date_range(
        start_date=args.start_date,
        end_date=args.end_date,
        dataset_config=model_config['dataset_config'],
        is_training=True
    )

    # Prepare features for model training
    logger.info("Preparing features for model training")
    features = dataset.prepare_features(is_training=True)

    # Extract features and labels
    X = features.get('X')
    y = features.get('y')
    feature_names = features.get('feature_names')
    dates = features.get('dates')

    # Export training data if requested
    if args.export_data:
        logger.info(f"Exporting training data in {args.export_format} format")

        # Create metadata for export
        metadata = {
            'class_labels': CLASS_LABELS,
            'config': model_config,
            'preprocessing': {
                'train_test_split': TRAIN_TEST_SPLIT,
                'validation_split': VALIDATION_SPLIT
            }
        }

        # Add feature metadata if available
        if 'metadata' in features:
            metadata['features'] = features['metadata']

        export_training_data(
            X=X,
            y=y,
            feature_names=feature_names,
            dates=dates,
            output_dir=output_dir,
            export_format=args.export_format,
            metadata=metadata,
            label_list=dataset.label_list
        )
        return

    if X.size == 0 or y is None:
        logger.error("No valid features or labels found")
        return

    logger.info(f"Prepared {len(X)} samples with features shape {X.shape}")
    logger.info(f"Feature names: {feature_names}")

    # Split data into train and test sets
    X_train, X_test, y_train, _ = train_test_split(
        X, y, test_size=TRAIN_TEST_SPLIT, random_state=42
    )

    # Split train set into train and validation sets
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=VALIDATION_SPLIT, random_state=42
    )

    logger.info(f"Train set: {X_train.shape[0]} samples")
    logger.info(f"Validation set: {X_val.shape[0]} samples")
    logger.info(f"Test set: {X_test.shape[0]} samples")

    # Create model
    model = create_model(model_config)
    if model is None:
        logger.error(
            f"Failed to create model of type {model_config['model_type']}")
        return

    # Train model
    logger.info("Training model")
    metrics = model.train(
        X_train=X_train,
        y_train=y_train,
        X_val=X_val,
        y_val=y_val,
        output_dir=output_dir
    )

    # Save metrics
    metrics_path = os.path.join(output_dir, "metrics.json")

    # Convert NumPy arrays to Python native types
    metrics_converted = convert_numpy_to_python(metrics)

    with open(metrics_path, 'w') as f:
        json.dump(metrics_converted, f, indent=4)

    # Analyze feature importance if requested
    if args.feature_importance:
        logger.info("Analyzing feature importance...")

        # Skip feature importance for LSTM models as they don't provide direct feature importance
        if args.model_type == 'lstm':
            logger.warning(
                "Feature importance analysis is not supported for LSTM models")
            importance_results = {
                "error": "Feature importance not supported for LSTM models"}
        else:
            importance_results = analyze_feature_importance(
                model=model.model,  # Access the underlying model
                feature_names=feature_names,
                output_dir=output_dir,
                top_n=args.top_n_features,
                export_csv=True
            )

        # Add feature importance to metrics
        if "error" not in importance_results:
            metrics_converted["feature_importance"] = {
                "visualization_path": importance_results.get("visualization_path"),
                "export_path": importance_results.get("export_path")
            }

            # Update metrics file with feature importance information
            with open(metrics_path, 'w') as f:
                json.dump(metrics_converted, f, indent=4)

    logger.info(f"Model trained and saved to {output_dir}")
    logger.info(f"Metrics: {json.dumps(metrics_converted, indent=2)}")


if __name__ == "__main__":
    args = parse_args()
    train_model(args)
