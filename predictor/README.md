# SP500 Price Predictor

This module uses advanced models to predict future SP500 prices based on financial news data and historical price information.

## Features

- Loads financial news data from the past X days (configurable)
- Preprocesses news text and price data for model input
- Supports multiple model architectures:
  - LLM-based model (text-only)
  - Multimodal transformer model (text + price data)
- Evaluates model performance using financial metrics
- Generates predictions with confidence scores

## Project Structure

```
predictor/
├── data/
│   ├── __init__.py                 # Data module initialization
│   ├── news_dataset.py             # Dataset for news articles
│   ├── price_dataset.py            # Dataset for price data
│   ├── fin_dataset.py              # Combined financial dataset
│   ├── data_sampler.py             # Data sampling utilities
│   ├── feature_extractor.py        # Feature extraction for models
│   └── augmentation/               # Data augmentation module
│       ├── __init__.py             # Augmentation module initialization
│       ├── base.py                 # Base augmentation interface
│       ├── chunk_augmentation.py   # Chunk-based augmentation
│       └── factory.py              # Factory for creating augmentations
├── models/
│   ├── __init__.py                 # Models module initialization
│   ├── base_model.py               # Base model interface
│   ├── logistic_regression_model.py # Logistic Regression model
│   ├── gradient_boosting_model.py  # Gradient Boosting model
│   ├── lstm_model.py               # LSTM neural network model
│   └── model_factory.py            # Factory for creating models
├── utils/
│   ├── __init__.py                 # Utilities module initialization
│   ├── cli.py                      # Command-line interface utilities
│   ├── helpers.py                  # Helper functions
│   └── logging_config.py           # Logging configuration
├── predict/
│   ├── __init__.py                 # Prediction module initialization
│   └── predict_model.py            # Script for making predictions
├── indicator/
│   ├── __init__.py                 # Indicator module initialization
│   ├── indicator_model.py          # Market movement indicator model
│   ├── predict.py                  # Script for indicator predictions
│   ├── train.py                    # Script for training indicator model
│   └── analyze_data.py             # Data analysis for indicator model
├── tests/
│   ├── __init__.py                 # Tests module initialization
│   ├── test_models.py              # Tests for model implementations
│   └── run_tests.py                # Script for running all tests
├── __init__.py                     # Package initialization
├── __main__.py                     # Main entry point for the package
├── config.py                       # Configuration settings
├── train.py                        # Script for training models
└── README.md                       # Documentation
```

## Installation

1. Create and activate the conda environment:
```
conda activate llm
```

2. Install the required packages:
```
pip install -r predictor/requirements.txt
```

3. Install the package in development mode:
```
pip install -e .
```

## Usage

You can use the predictor package in two ways:

### 1. Using the main package entry point

```bash
# For training
python -m predictor train --model-type=logistic_regression --start-date=2023-01-01 --end-date=2023-12-31
```

```bash
# For prediction
python -m predictor predict --model-path=path/to/model
```

### 2. Using the individual scripts directly

#### Training the Model

To train the model on historical news and price data:

```bash
python -m predictor.train --model-type=logistic_regression --start-date=2023-01-01 --end-date=2023-12-31
```

Optional arguments:
- `--model-type`: Type of model to train (logistic_regression, gradient_boosting, lstm)
- `--start-date`: Start date for training data (YYYY-MM-DD)
- `--end-date`: End date for training data (YYYY-MM-DD)
- `--ticker`: Ticker symbol to use for price data (default: SPY)
- `--model-name`: Name for the trained model
- `--config-file`: Path to model configuration file (JSON)
- `--news-days`: Number of days of news data to use as features
- `--price-days`: Number of days of price data to use as features
- `--use-sentiment`: Use sentiment scores as features
- `--use-price-indicators`: Use price indicators as features
- `--output-dir`: Directory to save the trained model

#### Making Predictions

```bash
python -m predictor.predict.predict_model --model-path=path/to/model
```

Optional arguments:
- `--model-path`: Path to the trained model
- `--prediction-date`: Specific date to predict for (YYYY-MM-DD)
- `--start-date`: Start date for prediction range (YYYY-MM-DD)
- `--end-date`: End date for prediction range (YYYY-MM-DD)
- `--output-file`: Path to save prediction results
- `--format`: Output format (json or csv)

## Model Details

The predictor package supports multiple model types for SP500 price prediction:

### Data Augmentation

The predictor package includes a data augmentation module to address class imbalance issues:

- **Chunk-based Augmentation**: Treats each chunk of an article as a separate training sample
  - Utilizes chunk information already stored in article JSON files
  - Preserves the original article's classification label for each chunk
  - Configurable minimum chunk size to ensure quality
  - Helps increase minority class samples for better model training

### Logistic Regression Model

- Simple and interpretable classification model
- Input:
  - News sentiment scores from the past X days
  - Price features from the past Y days
- Processing:
  - Features are scaled and normalized
  - Logistic regression with L2 regularization
- Output: Classification into three classes (positive, negative, neutral)

### Gradient Boosting Model

- Ensemble learning method using decision trees
- Input:
  - News sentiment scores from the past X days
  - Price features from the past Y days
- Processing:
  - Features are combined and processed by gradient boosting
  - Trees are built sequentially to correct errors of previous trees
- Output: Classification into three classes (positive, negative, neutral)

### LSTM Model

- Deep learning model for sequence data
- Input:
  - Sequence of price features from the past Y days
  - News sentiment scores from the past X days
- Processing:
  - LSTM layers process the sequence data
  - Feed-forward layers combine features
- Output: Classification into three classes (positive, negative, neutral)

### Indicator Model

- Specialized model for predicting market movement from a single article
- Input:
  - Text of a financial news article
- Processing:
  - FinBERT encodes the article text
  - Classification head predicts market movement
- Output: Classification into three classes (positive, negative, neutral)

## Evaluation Metrics

- Accuracy: Percentage of correct predictions
- Precision: Ratio of true positives to all positive predictions
- Recall: Ratio of true positives to all actual positives
- F1 Score: Harmonic mean of precision and recall
- Confusion Matrix: Detailed breakdown of predictions by class
