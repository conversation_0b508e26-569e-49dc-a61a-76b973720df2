"""
Main entry point for the predictor package.

This module provides a command-line interface for training and running the SP500 price predictor.
It handles argument parsing and dispatches to the appropriate submodules.

Usage:
    python -m predictor train [options]
    python -m predictor predict [options]
"""

import sys
import argparse
import subprocess
from utils.logging_config import get_predictor_logger

# Configure logging
logger = get_predictor_logger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="SP500 price predictor",
        usage="python -m predictor {train,predict} [options]"
    )

    subparsers = parser.add_subparsers(dest="action", help="Action to perform")

    # Train subparser
    train_parser = subparsers.add_parser("train", help="Train a model")
    train_parser.add_argument("--model-type", type=str, required=True,
                              choices=["logistic_regression",
                                       "gradient_boosting", "lstm"],
                              help="Type of model to train")
    train_parser.add_argument("--start-date", type=str, required=True,
                              help="Start date for training data")
    train_parser.add_argument("--end-date", type=str, required=True,
                              help="End date for training data")
    train_parser.add_argument("--output-dir", type=str, default=None,
                              help="Directory to save the trained model")

    # Predict subparser
    predict_parser = subparsers.add_parser(
        "predict", help="Make predictions with a trained model")
    predict_parser.add_argument("--model-path", type=str, required=True,
                                help="Path to the trained model")
    predict_parser.add_argument("--prediction-date", type=str, default=None,
                                help="Specific date to predict for")
    predict_parser.add_argument("--output-file", type=str, default=None,
                                help="Path to save the prediction results")

    return parser.parse_args()


def run_train(args):
    """Run the training script with the specified arguments."""
    cmd = [sys.executable, "-m", "predictor.train"]

    # Add required arguments
    cmd.extend(["--model-type", args.model_type])
    cmd.extend(["--start-date", args.start_date])
    cmd.extend(["--end-date", args.end_date])

    # Add optional arguments
    if args.output_dir:
        cmd.extend(["--output-dir", args.output_dir])

    logger.info(f"Running command: {' '.join(cmd)}")
    return subprocess.call(cmd)


def run_predict(args):
    """Run the prediction script with the specified arguments."""
    cmd = [sys.executable, "-m", "predictor.predict.predict_model"]

    # Add required arguments
    cmd.extend(["--model-path", args.model_path])

    # Add optional arguments
    if args.prediction_date:
        cmd.extend(["--prediction-date", args.prediction_date])

    if args.output_file:
        cmd.extend(["--output-file", args.output_file])

    logger.info(f"Running command: {' '.join(cmd)}")
    return subprocess.call(cmd)


def main():
    """Main function."""
    args = parse_args()

    if args.action == "train":
        return run_train(args)
    elif args.action == "predict":
        return run_predict(args)
    else:
        logger.error(f"Unknown action: {args.action}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
