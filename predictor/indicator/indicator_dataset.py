"""
Indicator dataset for the market movement indicator model.
Combines news and price datasets for joint processing.
"""

# Standard library imports
from typing import Optional, Dict, List

# Third-party imports
import pandas as pd
from torch.utils.data import Dataset
from transformers import AutoTokenizer

# Predictor imports
from predictor.data.news_dataset import NewsDataset
from predictor.data.price_dataset import PriceDataset
from predictor.config import (
    MODEL_MAX_LENGTH,
    SP500_TICKER,
    THRESHOLD
)
from utils.logging_config import get_predictor_logger

# Configure logger for this module
logger = get_predictor_logger(__name__)


class IndicatorDataset(Dataset):
    """
    Combined indicator dataset for the market movement indicator model.
    Simple wrapper that collects items from news and price datasets.
    """

    def __init__(
        self,
        start_date: str,
        end_date: str,
        is_training: bool = True,
        **kwargs
    ):
        """
        Initialize the indicator dataset.

        Args:
            samples: List of samples.
            news_dataset: NewsDataset instance with loaded and processed news data.
            price_dataset: PriceDataset instance with loaded and processed price data.
            is_training: Whether this dataset is for training.
        """
        self.start_date = start_date
        self.end_date = end_date
        news_dataset = NewsDataset(
            start_date,
            end_date,
            use_augmentation=kwargs.get('use_augmentation', False),
            augmentation_techniques=kwargs.get(
                'augmentation_techniques', 'chunk')
        )

        # Create price dataset
        price_dataset = PriceDataset(
            start_date,
            end_date,
            ticker=kwargs.get('ticker', SP500_TICKER)
        )

        filter_by_sentiment = kwargs.get('filter_by_sentiment', False)
        threshold = kwargs.get('threshold', THRESHOLD)

        self.tokenizer = kwargs.get('tokenizer', None)
        self.samples = self.generate_samples(
            news_dataset, price_dataset, is_training, filter_by_sentiment, threshold
        )

        logger.info(
            f"Created IndicatorDataset with {len(self.samples)} samples")

    def generate_samples(
        self,
        news_dataset: NewsDataset,
        price_dataset: PriceDataset,
        is_training: bool = True,
        filter_by_sentiment: bool = False,
        threshold: float = THRESHOLD
    ) -> List[Dict]:
        """
        Create samples for the indicator model.

        Args:
            news_dataset: NewsDataset instance
            price_dataset: PriceDataset instance
            is_training: Whether this dataset is for training

        Returns:
            List of sample dictionaries with article features and label information
            (if is_training=True)
        """
        samples = []
        articles_by_date = news_dataset.articles_by_date
        logger.info(
            f"Processing articles for {len(articles_by_date)} dates...")

        # Process each date
        for date in articles_by_date.keys():
            # Get individual article features for this date
            articles = news_dataset.get_articles_for_date(date)

            # Skip if no articles were found or processed
            if not articles:
                continue

            # Create a sample for each article
            for article_data in articles:
                article_text = news_dataset.extract_article_text(article_data)

                sentiment_data = article_data.get('article_metadata', {}).get(
                    'sentiment_analysis', {})
                sentiment_label = sentiment_data.get(
                    'sentiment', 'neutral')
                sentiment_scores = sentiment_data.get('scores', {})

                # Calculate sentiment score as (positive - negative)
                sentiment_score = sentiment_scores['positive'] - \
                    sentiment_scores['negative']

                # Create article sample
                article_sample = {
                    'date': date,
                    'article_data': article_data,
                    'article_text': article_text,
                    'sentiment': sentiment_label,
                    'sentiment_score': sentiment_score
                }
                if self.tokenizer:
                    encoding = self.tokenizer(
                        article_text,
                        truncation=True,
                        padding='max_length',
                        max_length=MODEL_MAX_LENGTH,
                        return_tensors='pt')
                    article_sample['input_ids'] = encoding['input_ids'].squeeze(
                        0)
                    article_sample['attention_mask'] = encoding['attention_mask'].squeeze(
                        0)

                # Add classification label information only if training
                if is_training:
                    # Get label information based on whether we're filtering by sentiment
                    label_info = None

                    if filter_by_sentiment:
                        # Use sentiment-filtered label method
                        label_info = price_dataset.get_sentiment_filtered_label_for_date(
                            date, sentiment_label, threshold)
                    else:
                        # Use regular label method
                        label_info = price_dataset.get_classification_label_for_date(
                            date, threshold)

                    # Skip this article if no label info is available
                    if label_info is None:
                        continue

                    # Add label information to the sample
                    article_sample.update({
                        'label': label_info['label'],
                        # Add 'labels' for compatibility with Hugging Face Trainer
                        'labels': label_info['label'],
                        'label_name': label_info['label_name'],
                        'return_value': label_info['return_value'],
                        'next_trading_date': label_info['next_trading_date']
                    })

                samples.append(article_sample)

        logger.info(f"Created {len(samples)} samples")
        return samples

    def __len__(self):
        """Return the number of samples."""
        return len(self.samples)

    def __getitem__(self, idx):
        """
        Get item at the specified index.

        Args:
            idx: Index of the sample to retrieve.

        Returns:
            Dictionary with sample data for the indicator model:
            date, article data, and label information (if is_training was True during sample creation).
        """
        if idx < 0 or idx >= len(self.samples):
            raise IndexError(
                f"Index {idx} out of range for dataset with {len(self.samples)} samples")

        # Get the sample at the specified index
        sample = self.samples[idx]

        return sample
