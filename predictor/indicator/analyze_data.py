"""
<PERSON><PERSON><PERSON> for analyzing and sampling training data for the indicator model.

This script provides functionality to:
1. Analyze the distribution of samples by label, date, and source
2. Sample training data with various strategies (balanced, stratified, etc.)
3. Visualize dataset characteristics
"""

# Standard library imports
from collections import Counter, defaultdict
import os
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# Third-party imports
from matplotlib import pyplot as plt
from transformers import AutoTokenizer

# Predictor imports
from predictor.data.data_sampler import analyze_dataset
from predictor.config import (
    OUTPUT_DIR,
    BASE_MODEL_NAME,
    CLASS_LABELS,
    DATE_FORMAT
)
from predictor.indicator.indicator_dataset import IndicatorDataset
from utils.logging_config import get_predictor_logger
from predictor.utils.sampling_utils import apply_sampling_strategies, get_sampling_summary

# Configure logger for this module
logger = get_predictor_logger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze and sample training data for the indicator model")

    # Date range options
    parser.add_argument("--start_date", type=str, required=True,
                        help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end_date", type=str, required=True,
                        help="End date in YYYY-MM-DD format")

    # Model options
    parser.add_argument("--model_name", type=str, default=BASE_MODEL_NAME,
                        help="Name of the pre-trained model to use")
    parser.add_argument("--output_dir", type=str,
                        help="Directory to save analysis results and sampled data")

    # Dataset options
    parser.add_argument("--use_processed_text", action="store_true",
                        help="Use processed text fields if available")
    parser.add_argument("--filter_by_sentiment", action="store_true",
                        help="Filter samples based on sentiment (positive samples with positive sentiment, negative samples with negative sentiment)")

    # Sampling options
    parser.add_argument("--sampling_strategy", type=str,
                        choices=["none", "by_ratio", "by_date",
                                 "by_source", "by_sentiment_score", "combined"],
                        default="none", help="Sampling strategy to use")
    parser.add_argument("--keep_positive_negative", action="store_true",
                        help="Keep all positive and negative samples and only sample from neutral class")
    parser.add_argument("--max_samples_per_class", type=int, default=None,
                        help="Maximum number of samples per class for balanced sampling")
    parser.add_argument("--max_samples_per_date", type=int, default=100,
                        help="Maximum number of samples per date for date-based sampling")
    parser.add_argument("--max_samples_per_source", type=int, default=None,
                        help="Maximum number of samples per source for source-based sampling")
    parser.add_argument("--sentiment_score_threshold", type=float, default=0.5,
                        help="Sentiment score threshold for sentiment-based sampling")
    parser.add_argument("--positive_ratio", type=float, default=0.33,
                        help="Label ratio for positive samples (class 0) in ratio-based sampling")
    parser.add_argument("--negative_ratio", type=float, default=0.33,
                        help="Label ratio for negative samples (class 1) in ratio-based sampling")
    parser.add_argument("--neutral_ratio", type=float, default=0.34,
                        help="Label ratio for neutral samples (class 2) in ratio-based sampling")

    # Export options
    parser.add_argument("--export_data", action="store_true",
                        help="Export sampled data to a file")
    parser.add_argument("--export_format", type=str, choices=["json", "csv"], default="json",
                        help="Format for exporting data")

    return parser.parse_args()


def setup_output_directory(args) -> str:
    """
    Set up the output directory for analysis results and sampled data.

    Args:
        args: Command line arguments.

    Returns:
        Path to the output directory.
    """
    if args.output_dir:
        output_dir = args.output_dir
    else:
        # Create a timestamped directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(
            OUTPUT_DIR, f"indicator_analysis_{timestamp}")

    # Create the directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save the arguments
    args_dict = vars(args)
    args_path = os.path.join(output_dir, "analysis_args.json")
    with open(args_path, 'w') as f:
        json.dump(args_dict, f, indent=2)

    logger.info(f"Set up output directory at {output_dir}")
    return output_dir


def export_dataset(samples: List[Dict], output_dir: str, export_format: str = "json"):
    """
    Export dataset to a file.

    Args:
        samples: List of samples to export
        output_dir: Directory to save the exported data
        export_format: Format to export the data in ('json' or 'csv')
    """
    # Create data directory if it doesn't exist
    data_dir = os.path.join(output_dir, "data")
    os.makedirs(data_dir, exist_ok=True)

    # Prepare data for export
    export_data = []

    # Check if samples are available
    if not samples or len(samples) == 0:
        logger.warning("No samples available for export")
        return

    # Process each sample
    for sample in samples:
        article_data = sample.get('article_data', {})

        # Get article text (either from pre-processed or extract it)
        if 'article_text' in sample:
            article_text = sample['article_text']
        else:
            article_text = ""

        # Get sentiment data
        sentiment = sample.get('sentiment', 'neutral')
        sentiment_score = sample.get('sentiment_score', 0.0)
        sentiment_scores = sample.get(
            'sentiment_scores', {'positive': 0.0, 'negative': 0.0, 'neutral': 1.0})

        # Create a simplified record for export
        record = {
            'date': sample.get('date', ''),
            'next_trading_date': sample.get('next_trading_date', ''),
            'return_value': sample.get('return_value', 0.0),
            'label': sample.get('label', 2),  # Default to neutral if not found
            # Default to neutral if not found
            'label_name': sample.get('label_name', CLASS_LABELS[2]),
            'sentiment': sentiment,
            'sentiment_score': sentiment_score,
            'sentiment_positive': sentiment_scores.get('positive', 0.0),
            'sentiment_negative': sentiment_scores.get('negative', 0.0),
            'sentiment_neutral': sentiment_scores.get('neutral', 0.0),
            'title': article_data.get('title', ''),
            'summary': article_data.get('summary', ''),
            'url': article_data.get('url', ''),
            'source': article_data.get('source', ''),
            'article_text': article_text
        }

        export_data.append(record)

    # Export data in the requested format
    if export_format == "json":
        # Save as JSON
        json_path = os.path.join(data_dir, "sampled_data.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Exported {len(export_data)} samples to {json_path}")

    elif export_format == "csv":
        # Save as CSV
        import csv

        csv_path = os.path.join(data_dir, "sampled_data.csv")
        with open(csv_path, 'w', encoding='utf-8', newline='') as f:
            # Define CSV fields
            fieldnames = [
                'date', 'next_trading_date', 'return_value', 'label',
                'label_name', 'sentiment', 'sentiment_score', 'sentiment_positive',
                'sentiment_negative', 'sentiment_neutral', 'title', 'summary',
                'url', 'source', 'article_text'
            ]

            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for record in export_data:
                writer.writerow(record)

        logger.info(f"Exported {len(export_data)} samples to {csv_path}")

    # Also export a sample file with fewer records and truncated text for quick inspection
    sample_data = export_data[:10] if len(export_data) > 10 else export_data

    # Truncate article text for the sample file
    for record in sample_data:
        if len(record['article_text']) > 500:
            record['article_text'] = record['article_text'][:500] + "..."

    sample_path = os.path.join(data_dir, f"sample_data.{export_format}")

    if export_format == "json":
        with open(sample_path, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2, ensure_ascii=False)
    else:
        import csv
        with open(sample_path, 'w', encoding='utf-8', newline='') as f:
            fieldnames = [
                'date', 'next_trading_date', 'return_value', 'label',
                'label_name', 'sentiment', 'sentiment_score', 'sentiment_positive',
                'sentiment_negative', 'sentiment_neutral', 'title', 'summary',
                'url', 'source', 'article_text'
            ]
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            for record in sample_data:
                writer.writerow(record)

    logger.info(f"Exported {len(sample_data)} sample records to {sample_path}")


def analyze_dataset(samples: List[Dict], output_dir: Optional[str] = None) -> Dict:
    """
    Analyze the dataset and generate statistics.

    Args:
        samples: List of samples to analyze
        output_dir: Directory to save analysis results and plots

    Returns:
        Dictionary with analysis results
    """
    if not samples or len(samples) == 0:
        logger.warning("Samples list is empty")
        return {"error": "No samples available"}

    # Create output directory if provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        plots_dir = os.path.join(output_dir, "plots")
        os.makedirs(plots_dir, exist_ok=True)

    # Initialize statistics dictionary
    stats = {
        "total_samples": len(samples),
        "label_distribution": {},
        "date_distribution": {},
        "source_distribution": {},
        "sentiment_distribution": {},
        "sentiment_by_label": {},
        "samples_by_date_and_label": {},
    }

    # Collect data for analysis
    dates = []
    labels = []
    sources = []
    sentiments = []
    label_sentiment_pairs = []
    date_label_pairs = []

    for sample in samples:
        # Extract date
        date = sample.get('date', 'unknown')
        dates.append(date)

        # Extract label
        label = sample.get('label', 2)  # Default to neutral if not found
        label_name = sample.get('label_name', CLASS_LABELS[2])
        labels.append(label_name)

        # Extract source
        article_data = sample.get('article_data', {})
        source = article_data.get('source', 'unknown')
        sources.append(source)

        # Extract sentiment
        sentiment = sample.get('sentiment', 'neutral')
        sentiments.append(sentiment)

        # Track label-sentiment pairs
        label_sentiment_pairs.append((label_name, sentiment))

        # Track date-label pairs
        date_label_pairs.append((date, label_name))

    # Calculate label distribution
    label_counts = Counter(labels)
    total_samples = len(labels)
    for label, count in label_counts.items():
        stats["label_distribution"][label] = {
            "count": count,
            "percentage": (count / total_samples) * 100 if total_samples > 0 else 0
        }

    # Calculate date distribution
    date_counts = Counter(dates)
    stats["date_distribution"] = {
        date: count for date, count in date_counts.items()}

    # Calculate source distribution
    source_counts = Counter(sources)
    stats["source_distribution"] = {
        source: count for source, count in source_counts.most_common(10)}

    # Calculate sentiment distribution
    sentiment_counts = Counter(sentiments)
    for sentiment, count in sentiment_counts.items():
        stats["sentiment_distribution"][sentiment] = {
            "count": count,
            "percentage": (count / total_samples) * 100 if total_samples > 0 else 0
        }

    # Calculate sentiment distribution by label
    sentiment_by_label = defaultdict(Counter)
    for label, sentiment in label_sentiment_pairs:
        sentiment_by_label[label][sentiment] += 1

    for label, counter in sentiment_by_label.items():
        label_total = sum(counter.values())
        stats["sentiment_by_label"][label] = {
            sentiment: {
                "count": count,
                "percentage": (count / label_total) * 100 if label_total > 0 else 0
            }
            for sentiment, count in counter.items()
        }

    # Calculate samples by date and label
    samples_by_date_label = defaultdict(Counter)
    for date, label in date_label_pairs:
        samples_by_date_label[date][label] += 1

    stats["samples_by_date_and_label"] = {
        date: dict(counter) for date, counter in samples_by_date_label.items()
    }

    # Generate plots if output directory is provided
    if output_dir:
        # Plot label distribution
        plot_label_distribution(stats["label_distribution"], os.path.join(
            plots_dir, "label_distribution.png"))

        # Plot sentiment distribution
        plot_sentiment_distribution(stats["sentiment_distribution"], os.path.join(
            plots_dir, "sentiment_distribution.png"))

        # Plot sentiment by label
        plot_sentiment_by_label(stats["sentiment_by_label"], os.path.join(
            plots_dir, "sentiment_by_label.png"))

        # Plot samples by date
        plot_samples_by_date(stats["date_distribution"], os.path.join(
            plots_dir, "samples_by_date.png"))

        # Plot samples by source
        plot_samples_by_source(stats["source_distribution"], os.path.join(
            plots_dir, "samples_by_source.png"))

        # Save statistics to file
        stats_path = os.path.join(output_dir, "dataset_analysis.json")
        with open(stats_path, 'w') as f:
            json.dump(stats, f, indent=2)
        logger.info(f"Saved dataset analysis to {stats_path}")

    return stats


def plot_label_distribution(label_distribution: Dict, output_path: str):
    """
    Plot the distribution of samples by label.

    Args:
        label_distribution: Dictionary with label distribution statistics
        output_path: Path to save the plot
    """
    labels = []
    counts = []
    percentages = []

    for label, stats in label_distribution.items():
        labels.append(label)
        counts.append(stats["count"])
        percentages.append(stats["percentage"])

    # Create figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

    # Plot counts
    bars = ax1.bar(labels, counts)
    ax1.set_title("Sample Count by Label")
    ax1.set_xlabel("Label")
    ax1.set_ylabel("Count")
    ax1.grid(axis='y', linestyle='--', alpha=0.7)

    # Add count labels on bars
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{int(height)}', ha='center', va='bottom')

    # Plot percentages
    bars = ax2.bar(labels, percentages)
    ax2.set_title("Sample Percentage by Label")
    ax2.set_xlabel("Label")
    ax2.set_ylabel("Percentage (%)")
    ax2.grid(axis='y', linestyle='--', alpha=0.7)

    # Add percentage labels on bars
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{height:.1f}%', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def plot_sentiment_distribution(sentiment_distribution: Dict, output_path: str):
    """
    Plot the distribution of samples by sentiment.

    Args:
        sentiment_distribution: Dictionary with sentiment distribution statistics
        output_path: Path to save the plot
    """
    sentiments = []
    counts = []
    percentages = []

    for sentiment, stats in sentiment_distribution.items():
        sentiments.append(sentiment)
        counts.append(stats["count"])
        percentages.append(stats["percentage"])

    # Create figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))

    # Plot counts
    bars = ax1.bar(sentiments, counts)
    ax1.set_title("Sample Count by Sentiment")
    ax1.set_xlabel("Sentiment")
    ax1.set_ylabel("Count")
    ax1.grid(axis='y', linestyle='--', alpha=0.7)

    # Add count labels on bars
    for bar in bars:
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{int(height)}', ha='center', va='bottom')

    # Plot percentages
    bars = ax2.bar(sentiments, percentages)
    ax2.set_title("Sample Percentage by Sentiment")
    ax2.set_xlabel("Sentiment")
    ax2.set_ylabel("Percentage (%)")
    ax2.grid(axis='y', linestyle='--', alpha=0.7)

    # Add percentage labels on bars
    for bar in bars:
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{height:.1f}%', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def plot_sentiment_by_label(sentiment_by_label: Dict, output_path: str):
    """
    Plot the distribution of sentiment for each label.

    Args:
        sentiment_by_label: Dictionary with sentiment distribution by label
        output_path: Path to save the plot
    """
    # Determine all unique sentiments across all labels
    all_sentiments = set()
    for label_stats in sentiment_by_label.values():
        all_sentiments.update(label_stats.keys())
    all_sentiments = sorted(list(all_sentiments))

    # Create a figure with a subplot for each label
    num_labels = len(sentiment_by_label)
    fig, axes = plt.subplots(1, num_labels, figsize=(15, 6))

    # Handle case with only one label
    if num_labels == 1:
        axes = [axes]

    # Plot sentiment distribution for each label
    for i, (label, sentiment_stats) in enumerate(sentiment_by_label.items()):
        ax = axes[i]

        # Extract sentiment counts and percentages
        sentiments = []
        percentages = []

        for sentiment in all_sentiments:
            sentiments.append(sentiment)
            if sentiment in sentiment_stats:
                percentages.append(sentiment_stats[sentiment]["percentage"])
            else:
                percentages.append(0)

        # Plot percentages
        bars = ax.bar(sentiments, percentages)
        ax.set_title(f"Sentiment Distribution for {label.capitalize()}")
        ax.set_xlabel("Sentiment")
        ax.set_ylabel("Percentage (%)")
        ax.grid(axis='y', linestyle='--', alpha=0.7)

        # Add percentage labels on bars
        for bar in bars:
            height = bar.get_height()
            if height > 0:
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:.1f}%', ha='center', va='bottom')

        # Rotate x-axis labels if needed
        plt.setp(ax.get_xticklabels(), rotation=45,
                 ha="right", rotation_mode="anchor")

    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def plot_samples_by_date(date_distribution: Dict, output_path: str):
    """
    Plot the number of samples by date.

    Args:
        date_distribution: Dictionary with date distribution statistics
        output_path: Path to save the plot
    """
    # Sort dates
    sorted_dates = sorted(date_distribution.keys())
    counts = [date_distribution[date] for date in sorted_dates]

    # Format dates for display
    display_dates = []
    for date in sorted_dates:
        try:
            dt = datetime.strptime(date, DATE_FORMAT)
            display_dates.append(dt.strftime("%m-%d"))
        except:
            display_dates.append(date)

    # Create figure
    plt.figure(figsize=(12, 6))
    bars = plt.bar(display_dates, counts)
    plt.title("Sample Count by Date")
    plt.xlabel("Date")
    plt.ylabel("Count")
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    # Add count labels on bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{int(height)}', ha='center', va='bottom')

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha="right")

    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def plot_samples_by_source(source_distribution: Dict, output_path: str):
    """
    Plot the number of samples by source.

    Args:
        source_distribution: Dictionary with source distribution statistics
        output_path: Path to save the plot
    """
    # Sort sources by count (descending)
    sorted_sources = sorted(source_distribution.items(),
                            key=lambda x: x[1], reverse=True)
    sources = [item[0] for item in sorted_sources]
    counts = [item[1] for item in sorted_sources]

    # Create figure
    plt.figure(figsize=(12, 6))
    bars = plt.bar(sources, counts)
    plt.title("Sample Count by Source")
    plt.xlabel("Source")
    plt.ylabel("Count")
    plt.grid(axis='y', linestyle='--', alpha=0.7)

    # Add count labels on bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                 f'{int(height)}', ha='center', va='bottom')

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha="right")

    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def main():
    """Main function for analyzing and sampling training data."""
    args = parse_args()

    # Set up output directory
    output_dir = setup_output_directory(args)

    # Initialize tokenizer
    logger.info(f"Initializing tokenizer with {args.model_name}")
    tokenizer = AutoTokenizer.from_pretrained(args.model_name)

    # Create dataset using FinDataset for the indicator model
    logger.info(
        f"Creating dataset with date range from {args.start_date} to {args.end_date}")

    # Create dataset config for indicator model
    dataset_config = {
        'model_type': 'indicator',
        'tokenizer': tokenizer,
        'threshold': 1.0,  # 1% threshold for classification
        'use_processed_text': args.use_processed_text,
        'filter_by_sentiment': args.filter_by_sentiment
    }

    dataset = IndicatorDataset.from_date_range(
        start_date=args.start_date,
        end_date=args.end_date,
        dataset_config=dataset_config,
        is_training=True
    )
    samples = dataset.samples

    # Analyze original dataset
    logger.info("Analyzing original dataset")
    original_analysis_dir = os.path.join(output_dir, "original_analysis")
    os.makedirs(original_analysis_dir, exist_ok=True)
    original_stats = analyze_dataset(samples, original_analysis_dir)

    # Apply sampling strategy
    sampled_samples = samples
    if args.sampling_strategy != "none":
        # Apply sampling strategies using the utility function
        sampled_samples = apply_sampling_strategies(samples, args, logger)

        # Analyze sampled dataset
        logger.info("Analyzing sampled dataset")
        sampled_analysis_dir = os.path.join(output_dir, "sampled_analysis")
        os.makedirs(sampled_analysis_dir, exist_ok=True)
        sampled_stats = analyze_dataset(sampled_samples, sampled_analysis_dir)

    # Export sampled data if requested
    if args.export_data:
        logger.info(f"Exporting sampled data in {args.export_format} format")
        export_dataset(sampled_samples, output_dir, args.export_format)

    # Print summary
    print("\nAnalysis Complete!")
    print(f"Results saved to: {output_dir}")
    print(f"Original dataset: {len(samples)} samples")

    if args.sampling_strategy != "none":
        print(f"Sampled dataset: {len(sampled_samples)} samples")
        print(f"Sampling strategy: {args.sampling_strategy}")

        # Get sampling summary using the utility function
        summary = get_sampling_summary(args)

        # Print summary details
        if "keep_positive_negative" in summary:
            print("  Keep all positive and negative samples: Yes")

        if "class_ratios" in summary:
            print(f"  Class ratios: {summary['class_ratios']}")

        if "max_samples_per_class" in summary:
            print(
                f"  Max samples per class: {summary['max_samples_per_class']}")

        if "max_samples_per_date" in summary:
            print(f"  Max samples per date: {summary['max_samples_per_date']}")

        if "max_samples_per_source" in summary:
            print(
                f"  Max samples per source: {summary['max_samples_per_source']}")

        if "sentiment_score_threshold" in summary:
            print(
                f"  Sentiment score threshold: {summary['sentiment_score_threshold']}")

    print("\nOriginal Label Distribution:")
    for label, stats in original_stats["label_distribution"].items():
        print(
            f"  {label.capitalize()}: {stats['count']} samples ({stats['percentage']:.1f}%)")

    # Print sampled label distribution if available
    if args.sampling_strategy != "none":
        print("\nSampled Label Distribution:")
        for label, stats in sampled_stats["label_distribution"].items():
            print(
                f"  {label.capitalize()}: {stats['count']} samples ({stats['percentage']:.1f}%)")


if __name__ == "__main__":
    main()
