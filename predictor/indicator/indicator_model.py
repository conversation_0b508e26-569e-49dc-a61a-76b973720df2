"""
Indicator model implementation for market movement prediction.
Uses a transformer model to classify articles into three categories:
- Positive: >1% return
- Negative: >1% loss
- Neutral: Between -1% and 1% change

Features:
- Uses a pre-trained transformer model for text encoding
- Fine-tunes the model for the specific classification task
- Provides confidence scores for each class
- Supports different transformer model architectures from Hugging Face
"""

# Standard library imports
import os
import json
from datetime import datetime
from typing import Dict, Optional, Union

# Third-party imports
import torch
import numpy as np
from torch.utils.data import Dataset
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
from transformers import (
    AutoModelForSequenceClassification,
    AutoTokenizer,
    Trainer,
    TrainingArguments,
    EvalPrediction,
    DataCollatorWithPadding
)
import torch.nn.functional as F

# Predictor imports
from predictor.config import (
    BASE_MODEL_NAME,
    BATCH_SIZE,
    FINE_TUNED_LAYERS,
    LEARNING_RATE,
    MODEL_MAX_LENGTH,
    NUM_EPOCHS,
    WARMUP_RATIO,
    WEI<PERSON>HT_DECAY,
    MODELS_DIR,
    DATE_FORMAT,
    CLASS_LABELS
)
from utils.logging_config import get_predictor_logger

# Configure logger for this module
logger = get_predictor_logger(__name__)


class WeightedLossTrainer(Trainer):
    """
    Custom Trainer class that supports weighted loss functions for handling class imbalance.
    """

    def __init__(self, class_weights=None, **kwargs):
        """
        Initialize the weighted loss trainer.

        Args:
            class_weights: Optional tensor of class weights for the loss function.
            **kwargs: Additional arguments to pass to the Trainer constructor.
        """
        self.class_weights = class_weights
        super().__init__(**kwargs)

    def compute_loss(self, model, inputs, return_outputs=False):
        """
        Compute the weighted loss for the batch.

        Args:
            model: The model to compute the loss for.
            inputs: The inputs to the model.
            return_outputs: Whether to return the outputs along with the loss.

        Returns:
            The loss or a tuple of (loss, outputs) if return_outputs is True.
        """
        labels = inputs.pop("labels")
        outputs = model(**inputs)
        logits = outputs.logits

        if self.class_weights is not None:
            # Use weighted cross entropy loss
            loss = F.cross_entropy(
                logits.view(-1, model.config.num_labels),
                labels.view(-1),
                weight=self.class_weights.to(logits.device)
            )
        else:
            # Use standard cross entropy loss
            loss = F.cross_entropy(
                logits.view(-1, model.config.num_labels),
                labels.view(-1)
            )

        return (loss, outputs) if return_outputs else loss


class IndicatorModel:
    """
    Indicator model for market movement prediction.
    Uses a transformer model to classify articles into three categories:
    - Positive: >1% return
    - Negative: >1% loss
    - Neutral: Between -1% and 1% change
    """

    def __init__(
        self,
        model_name: str = BASE_MODEL_NAME,
        model_path: Optional[str] = None,
        tokenizer: Optional[AutoTokenizer] = None,
        use_class_weights: bool = False,
        class_weights: Optional[torch.Tensor] = None
    ):
        """
        Initialize the indicator model.

        Args:
            model_name: Name of the pre-trained model to use.
            model_path: Path to a saved model. If provided, loads the model from this path.
            tokenizer: Optional pre-initialized tokenizer. If not provided, will create a new one.
            use_class_weights: Whether to use class weights in loss function to handle class imbalance.
            class_weights: Optional tensor of class weights for the loss function.
        """
        self.model_name = model_name
        self.class_weights = class_weights
        self.use_class_weights = use_class_weights

        # Set device
        self.device = torch.device(
            "cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Using device: {self.device}")

        # Load tokenizer
        if tokenizer is not None:
            self.tokenizer = tokenizer
            logger.info(f"Using provided tokenizer for {model_name}")
        else:
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            logger.info(f"Initialized new tokenizer for {model_name}")

        # Load or initialize model
        if model_path:
            self._load_model(model_path)
        else:
            self._initialize_model()

    @classmethod
    def from_config(cls, model_config: Dict):
        """
        Create an IndicatorModel instance from a model configuration dictionary.

        Args:
            model_config: Dictionary containing model configuration parameters.

        Returns:
            IndicatorModel instance.
        """
        model_name = model_config.get('model_name', BASE_MODEL_NAME)
        tokenizer = model_config.get('tokenizer')
        use_class_weights = model_config.get('use_class_weights', False)
        class_weights = model_config.get('class_weights', None)
        return cls(model_name, tokenizer=tokenizer, use_class_weights=use_class_weights, class_weights=class_weights)

    def _initialize_model(self):
        """Initialize a new model for sequence classification with frozen layers."""
        logger.info(f"Initializing new model from {self.model_name}")

        # Load pre-trained model with classification head
        self.model = AutoModelForSequenceClassification.from_pretrained(
            self.model_name,
            # Three classes: positive, negative, neutral
            num_labels=len(CLASS_LABELS)
        )

        # Freeze all layers except the last FINE_TUNED_LAYERS layers and the classification head
        # Most transformer models have a base model (e.g., bert, roberta) that contains the layers
        if hasattr(self.model, 'base_model'):
            base_model = self.model.base_model

            # Get the total number of layers
            if hasattr(base_model, 'encoder') and hasattr(base_model.encoder, 'layer'):
                # BERT-like models
                num_layers = len(base_model.encoder.layer)

                # Freeze embeddings
                for param in base_model.embeddings.parameters():
                    param.requires_grad = False

                # Freeze all but the last FINE_TUNED_LAYERS layers
                for i, layer in enumerate(base_model.encoder.layer):
                    if i < num_layers - FINE_TUNED_LAYERS:  # Freeze all except the last layer
                        for param in layer.parameters():
                            param.requires_grad = False

                logger.info(
                    f"Froze embeddings and {num_layers - FINE_TUNED_LAYERS} out of {num_layers} encoder layers")
            else:
                logger.warning(
                    "Could not identify transformer layers structure. No layers were frozen.")

            # Log parameter names and their requires_grad status
            for name, param in base_model.named_parameters():
                logger.info(f"{name} - Requires grad: {param.requires_grad}")
        else:
            logger.warning(
                "Model does not have a base_model attribute. No layers were frozen.")

        # Move model to device
        self.model = self.model.to(self.device)

    def _load_model(self, model_path: str):
        """
        Load a saved model.

        Args:
            model_path: Path to the saved model.
        """
        logger.info(f"Loading model from {model_path}")

        try:
            # Load model
            self.model = AutoModelForSequenceClassification.from_pretrained(
                model_path)

            # Move model to device
            self.model = self.model.to(self.device)

            # Load metadata if available
            metadata_path = os.path.join(model_path, "metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    self.metadata = json.load(f)
                logger.info(f"Loaded model metadata: {self.metadata}")

                # Load class weights if available in metadata
                if 'class_weights' in self.metadata and self.metadata['class_weights']:
                    self.class_weights = torch.tensor(
                        self.metadata['class_weights'], dtype=torch.float32)
                    logger.info(
                        f"Loaded class weights from metadata: {self.class_weights}")
            else:
                self.metadata = {}
                logger.warning(f"No metadata found for model at {model_path}")

        except Exception as e:
            logger.error(f"Error loading model from {model_path}: {e}")
            logger.info(
                f"Initializing new model from {self.model_name} instead")
            self._initialize_model()

    def _compute_metrics(self, eval_pred: EvalPrediction) -> Dict[str, float]:
        """
        Compute evaluation metrics for the model.

        Args:
            eval_pred: Evaluation predictions from the Trainer.

        Returns:
            Dictionary of metrics.
        """
        logits, labels = eval_pred.predictions, eval_pred.label_ids
        preds = np.argmax(logits, axis=1)

        # Calculate metrics
        accuracy = accuracy_score(labels, preds)
        precision, recall, f1, _ = precision_recall_fscore_support(
            labels, preds, average=None, labels=[0, 1, 2]
        )

        # Calculate confusion matrix
        cm = confusion_matrix(labels, preds, labels=[0, 1, 2])

        # Create metrics dictionary
        metrics = {
            'accuracy': float(accuracy),
            'precision': {CLASS_LABELS[i]: float(precision[i]) for i in range(3)},
            'recall': {CLASS_LABELS[i]: float(recall[i]) for i in range(3)},
            'f1': {CLASS_LABELS[i]: float(f1[i]) for i in range(3)},
            'confusion_matrix': cm.tolist()
        }

        # Add individual metrics for Trainer to track
        for i, label in CLASS_LABELS.items():
            metrics[f'precision_{label}'] = float(precision[i])
            metrics[f'recall_{label}'] = float(recall[i])
            metrics[f'f1_{label}'] = float(f1[i])

        return metrics

    def train(
        self,
        train_dataset: Dataset,
        val_dataset: Dataset,
        output_dir: Optional[str] = None,
        test_dataset: Optional[Dataset] = None
    ) -> Dict[str, float]:
        """
        Train the model using Hugging Face's Trainer API.

        Args:
            train_dataset: Training dataset.
            val_dataset: Validation dataset.
            output_dir: Directory to save the model.
            test_dataset: Optional test dataset for final evaluation.

        Returns:
            Dictionary of evaluation metrics.
        """
        if test_dataset:
            logger.info(
                f"Training model with {len(train_dataset)} training samples, {len(val_dataset)} validation samples, and {len(test_dataset)} test samples")
        else:
            logger.info(
                f"Training model with {len(train_dataset)} training samples and {len(val_dataset)} validation samples")

        # Set output directory
        if output_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(
                MODELS_DIR, f"indicator_model_{timestamp}")

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Define training arguments
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=NUM_EPOCHS,
            per_device_train_batch_size=BATCH_SIZE,
            per_device_eval_batch_size=BATCH_SIZE,
            warmup_ratio=WARMUP_RATIO,
            weight_decay=WEIGHT_DECAY,
            learning_rate=LEARNING_RATE,
            evaluation_strategy="epoch",
            save_strategy="epoch",
            load_best_model_at_end=True,
            metric_for_best_model="accuracy",
            greater_is_better=True,
            logging_dir=os.path.join(output_dir, "logs"),
            logging_steps=10,
            report_to="none",  # Disable wandb, tensorboard, etc.
        )

        # Calculate class weights if not provided and use_class_weights is True
        if self.class_weights is None and self.use_class_weights:
            # Count samples by label
            label_counts = {0: 0, 1: 0, 2: 0}
            for sample in train_dataset:
                if 'label' in sample:
                    label = sample['label']
                    label_counts[label] = label_counts.get(label, 0) + 1

            # Calculate inverse frequency weights
            total_samples = sum(label_counts.values())
            if total_samples > 0:
                # Calculate weights as inverse of frequency
                weights = {
                    label: total_samples /
                    (count * len(label_counts)) if count > 0 else 1.0
                    for label, count in label_counts.items()
                }

                # Convert to tensor
                self.class_weights = torch.tensor(
                    [weights.get(i, 1.0) for i in range(3)],
                    dtype=torch.float32
                )

                logger.info(f"Calculated class weights: {self.class_weights}")
                logger.info(f"Class distribution: {label_counts}")
            else:
                logger.warning(
                    "No samples with labels found. Using equal class weights.")
                self.class_weights = torch.tensor(
                    [1.0, 1.0, 1.0], dtype=torch.float32)

        # Use a default data collator from transformers
        data_collator = DataCollatorWithPadding(tokenizer=self.tokenizer)

        # Initialize the WeightedLossTrainer
        trainer = WeightedLossTrainer(
            class_weights=self.class_weights,
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=val_dataset,
            compute_metrics=self._compute_metrics,
            data_collator=data_collator,
        )

        # Train the model
        logger.info("Starting model training with Hugging Face Trainer")
        trainer.train()

        # Evaluate the model
        logger.info("Evaluating model on validation dataset")
        eval_results = trainer.evaluate()

        # If test dataset is provided, evaluate on it as well
        if test_dataset:
            logger.info("Evaluating model on test dataset")
            test_results = trainer.evaluate(test_dataset)
            # Add test results to metrics with 'test_' prefix
            test_metrics = {f"test_{k}": v for k, v in test_results.items()}
            eval_results.update(test_metrics)

        # Save the best model
        logger.info(f"Saving best model to {output_dir}")
        trainer.save_model(output_dir)
        self.tokenizer.save_pretrained(output_dir)

        # Create metrics dictionary in the format expected by the rest of the code
        metrics = {
            'accuracy': eval_results.get('eval_accuracy', 0.0),
            'precision': {
                CLASS_LABELS[i]: eval_results.get(
                    f'eval_precision_{CLASS_LABELS[i]}', 0.0)
                for i in range(3)
            },
            'recall': {
                CLASS_LABELS[i]: eval_results.get(
                    f'eval_recall_{CLASS_LABELS[i]}', 0.0)
                for i in range(3)
            },
            'f1': {
                CLASS_LABELS[i]: eval_results.get(
                    f'eval_f1_{CLASS_LABELS[i]}', 0.0)
                for i in range(3)
            },
            'confusion_matrix': eval_results.get('eval_confusion_matrix', [[0, 0, 0], [0, 0, 0], [0, 0, 0]])
        }

        # Save metadata with metrics
        self.save(output_dir, metrics)

        return metrics

    def predict(self, article_text: str) -> Dict[str, Union[str, Dict[str, float]]]:
        """
        Predict market movement based on a single article.

        Args:
            article_text: Text of the article.

        Returns:
            Dictionary with prediction results.
        """
        if not article_text:
            logger.warning("Empty text provided for prediction")
            return {
                "prediction": CLASS_LABELS[2],  # neutral
                "scores": {CLASS_LABELS[0]: 0.0, CLASS_LABELS[1]: 0.0, CLASS_LABELS[2]: 1.0}
            }

        # Tokenize text
        inputs = self.tokenizer(
            article_text,
            return_tensors="pt",
            truncation=True,
            max_length=MODEL_MAX_LENGTH,
            padding=True
        )
        inputs = {k: v.to(self.device) for k, v in inputs.items()}

        # Get predictions
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(**inputs)
            predictions = torch.nn.functional.softmax(outputs.logits, dim=-1)
            scores = predictions[0].tolist()
            predicted_class = torch.argmax(predictions, dim=-1).item()
            predicted_label = CLASS_LABELS[predicted_class]

        # Create result dictionary
        result = {
            "prediction": predicted_label,
            "scores": {
                CLASS_LABELS[0]: scores[0],
                CLASS_LABELS[1]: scores[1],
                CLASS_LABELS[2]: scores[2]
            }
        }

        return result

    def save(self, output_dir: str, metrics: Optional[Dict] = None):
        """
        Save the model and metadata.

        Args:
            output_dir: Directory to save the model.
            metrics: Optional metrics to save with the model.
        """
        logger.info(f"Saving model metadata to {output_dir}")

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Note: The model itself is saved by the Trainer in the train method
        # We only need to save additional metadata here

        # Save metadata
        metadata = {
            'model_name': self.model_name,
            'date_saved': datetime.now().strftime(DATE_FORMAT),
            'class_labels': CLASS_LABELS,
            'frozen_layers': True,  # Indicate that we're using a model with frozen layers
            'fine_tuned_layers': FINE_TUNED_LAYERS,  # Number of fine-tuned layers
            'class_weights': self.class_weights.tolist() if self.class_weights is not None else None
        }

        if metrics:
            metadata['metrics'] = metrics

        metadata_path = os.path.join(output_dir, "metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)

        logger.info(f"Model metadata saved to {output_dir}")

    def load(self, model_path: str):
        """
        Load a saved model.

        Args:
            model_path: Path to the saved model.
        """
        self._load_model(model_path)
