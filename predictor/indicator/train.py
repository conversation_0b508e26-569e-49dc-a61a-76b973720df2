"""
<PERSON><PERSON><PERSON> for training the market movement indicator model.
Uses a classification approach to predict whether an article indicates
positive (>1% return), negative (>1% loss), or neutral market movement.
"""

# Standard library imports
import os
import json
import logging
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

# Third-party imports
import numpy as np
import matplotlib.pyplot as plt
import torch
from transformers import AutoTokenizer
from datasets import Dataset
from torch.utils.data import random_split

# Predictor imports
from predictor.indicator.indicator_model import IndicatorModel
from predictor.indicator.indicator_dataset import IndicatorDataset
from predictor.config import (
    MODEL_MAX_LENGTH,
    MODELS_DIR,
    DATE_FORMAT,
    BASE_MODEL_NAME,
    CLASS_LABELS,
    TRAIN_TEST_SPLIT,
    VALIDATION_SPLIT,
    THRESHOLD,
    SP500_TICKER
)
from utils.logging_config import get_predictor_logger
from predictor.utils.sampling_utils import apply_sampling_strategies, get_sampling_summary

# Configure logger for this module
logger = get_predictor_logger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Train market movement indicator model")

    # Date range options
    parser.add_argument("--start_date", type=str, required=True,
                        help="Start date in YYYY-MM-DD format")
    parser.add_argument("--end_date", type=str, required=True,
                        help="End date in YYYY-MM-DD format")

    # Model options
    parser.add_argument("--model_name", type=str, default=BASE_MODEL_NAME,
                        help="Name of the pre-trained model to use")
    parser.add_argument("--output_dir", type=str,
                        help="Directory to save the trained model")
    parser.add_argument("--threshold", type=float, default=THRESHOLD,
                        help="Threshold for classification")
    parser.add_argument("--ticker", type=str, default=SP500_TICKER,
                        help="Ticker symbol for the stock to use")

    # Training options
    parser.add_argument("--test_split", type=float,
                        default=TRAIN_TEST_SPLIT, help="Test split ratio")
    parser.add_argument("--val_split", type=float, default=VALIDATION_SPLIT,
                        help="Validation split ratio (of remaining data)")
    parser.add_argument("--filter_by_sentiment", action="store_true",
                        help="Filter samples based on sentiment (positive samples with positive sentiment, negative samples with negative sentiment)")
    parser.add_argument("--use_class_weights", action="store_true",
                        help="Use class weights in loss function to handle class imbalance")
    parser.add_argument("--class_weights", nargs='*', type=float, default=None,
                        help="Class weights for loss function (optional, overrides automatic calculation)")

    # Sampling options
    parser.add_argument("--sampling_strategy", type=str,
                        choices=["none", "by_ratio", "by_date",
                                 "by_source", "by_sentiment_score", "combined"],
                        default="none", help="Sampling strategy to use")
    parser.add_argument("--keep_positive_negative", action="store_true",
                        help="Keep all positive and negative samples and only sample from neutral class")
    parser.add_argument("--max_samples_per_date", type=int, default=100,
                        help="Maximum number of samples per date for date-based sampling")
    parser.add_argument("--max_samples_per_source", type=int, default=None,
                        help="Maximum number of samples per source for source-based sampling")
    parser.add_argument("--sentiment_score_threshold", type=float, default=0.5,
                        help="Sentiment score threshold for sentiment-based sampling")
    parser.add_argument("--positive_ratio", type=float, default=0.2,
                        help="Label ratio for positive samples (class 0) in ratio-based sampling")
    parser.add_argument("--negative_ratio", type=float, default=0.2,
                        help="Label ratio for negative samples (class 1) in ratio-based sampling")
    parser.add_argument("--neutral_ratio", type=float, default=0.6,
                        help="Label ratio for neutral samples (class 2) in ratio-based sampling")
    parser.add_argument("--upsample_minority_classes", action="store_true",
                        help="Upsample minority classes when there are fewer samples than required by the ratio")

    # Data augmentation options
    parser.add_argument("--use_augmentation", action="store_true",
                        help="Use data augmentation to address class imbalance")
    parser.add_argument("--augmentation_techniques", type=str, nargs="+", default=None,
                        help="List of augmentation techniques to use (e.g., chunk)")

    # Data export options
    parser.add_argument("--export_data", action="store_true",
                        help="Export training data to a file")
    parser.add_argument("--export_format", type=str, choices=["json", "csv"], default="json",
                        help="Format for exporting training data")

    return parser.parse_args()


def setup_output_directory(args) -> str:
    """
    Set up the output directory for the trained model.

    Args:
        args: Command line arguments.

    Returns:
        Path to the output directory.
    """
    if args.output_dir:
        output_dir = args.output_dir
    else:
        # Create a timestamped directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(MODELS_DIR, f"indicator_model_{timestamp}")

    # Create the directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save the training arguments
    args_dict = vars(args)
    args_path = os.path.join(output_dir, "training_args.json")
    with open(args_path, 'w') as f:
        json.dump(args_dict, f, indent=2)

    logger.info(f"Set up output directory at {output_dir}")
    return output_dir


def plot_training_metrics(metrics: Dict, output_dir: str):
    """
    Plot training metrics and save the plots.

    Args:
        metrics: Dictionary of metrics.
        output_dir: Directory to save the plots.
    """
    try:
        # Create plots directory
        plots_dir = os.path.join(output_dir, "plots")
        os.makedirs(plots_dir, exist_ok=True)

        # Plot confusion matrix
        cm = np.array(metrics['confusion_matrix'])
        fig, ax = plt.subplots(figsize=(8, 6))
        im = ax.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)
        ax.figure.colorbar(im, ax=ax)

        # Show all ticks and label them
        label_names = list(CLASS_LABELS.values())
        ax.set(
            xticks=np.arange(cm.shape[1]),
            yticks=np.arange(cm.shape[0]),
            xticklabels=label_names,
            yticklabels=label_names,
            title="Confusion Matrix",
            ylabel="True Label",
            xlabel="Predicted Label"
        )

        # Rotate the tick labels and set their alignment
        plt.setp(ax.get_xticklabels(), rotation=45,
                 ha="right", rotation_mode="anchor")

        # Loop over data dimensions and create text annotations
        thresh = cm.max() / 2.0
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                ax.text(
                    j, i, format(cm[i, j], 'd'),
                    ha="center", va="center",
                    color="white" if cm[i, j] > thresh else "black"
                )

        fig.tight_layout()
        plt.savefig(os.path.join(plots_dir, "confusion_matrix.png"))

        # Plot precision, recall, and F1 scores
        fig, ax = plt.subplots(figsize=(10, 6))
        label_names = list(CLASS_LABELS.values())
        x = np.arange(len(label_names))
        width = 0.25

        precision = [metrics['precision'][label] for label in label_names]
        recall = [metrics['recall'][label] for label in label_names]
        f1 = [metrics['f1'][label] for label in label_names]

        ax.bar(x - width, precision, width, label='Precision')
        ax.bar(x, recall, width, label='Recall')
        ax.bar(x + width, f1, width, label='F1')

        ax.set_ylabel('Score')
        ax.set_title('Precision, Recall, and F1 Scores by Class')
        ax.set_xticks(x)
        ax.set_xticklabels(label_names)
        ax.legend()

        fig.tight_layout()
        plt.savefig(os.path.join(plots_dir, "class_metrics.png"))

        logger.info(f"Saved metric plots to {plots_dir}")

    except Exception as e:
        logger.error(f"Error plotting metrics: {e}")


def save_dataset_stats(dataset: IndicatorDataset, output_dir: str):
    """
    Save dataset statistics.

    Args:
        dataset: IndicatorDataset instance configured for the indicator model.
        output_dir: Directory to save the statistics.
    """
    try:
        # Check if dataset has required attributes
        if not hasattr(dataset, 'start_date') or not hasattr(dataset, 'end_date'):
            logger.warning(
                "Dataset missing required attributes (start_date, end_date). Using defaults.")
            start_date = "unknown"
            end_date = "unknown"
        else:
            start_date = dataset.start_date
            end_date = dataset.end_date

        # Count samples by label class
        label_counts = {0: 0, 1: 0, 2: 0}

        # Check if dataset has samples
        if not hasattr(dataset, 'samples') or len(dataset.samples) == 0:
            logger.warning(
                "Dataset has no samples. Creating empty statistics.")
            total_samples = 0
        else:
            for sample in dataset.samples:
                # Default to neutral if label not found
                label = sample.get('label', 2)
                label_counts[label] += 1
            total_samples = len(dataset.samples)

        # Calculate percentages
        label_percentages = {
            CLASS_LABELS[label]: {
                'count': count,
                'percentage': (count / total_samples) * 100 if total_samples > 0 else 0
            }
            for label, count in label_counts.items()
        }

        # Create statistics dictionary
        stats = {
            'total_samples': total_samples,
            'date_range': {
                'start_date': start_date,
                'end_date': end_date
            },
            'label_distribution': label_percentages
        }

        # Add additional debug information
        if hasattr(dataset, 'news_dataset') and hasattr(dataset.news_dataset, 'articles_by_date'):
            stats['news_dates_count'] = len(
                dataset.news_dataset.articles_by_date)

        # Add augmentation information if available
        if hasattr(dataset, 'news_dataset'):
            news_dataset = dataset.news_dataset
            if hasattr(news_dataset, 'use_augmentation'):
                stats['augmentation'] = {
                    'enabled': news_dataset.use_augmentation,
                }
                if news_dataset.use_augmentation and hasattr(news_dataset, 'augmentation_techniques'):
                    stats['augmentation']['techniques'] = news_dataset.augmentation_techniques or [
                        "chunk"]
                    if hasattr(news_dataset, 'augmentations'):
                        stats['augmentation']['active_techniques'] = [
                            aug.get_name() for aug in news_dataset.augmentations]

        if hasattr(dataset, 'price_dataset') and hasattr(dataset.price_dataset, 'price_data'):
            price_data = dataset.price_dataset.price_data
            if not price_data.empty:
                stats['price_data'] = {
                    'count': len(price_data),
                    'date_range': {
                        'first_date': price_data.index[0].strftime(DATE_FORMAT) if len(price_data.index) > 0 else "none",
                        'last_date': price_data.index[-1].strftime(DATE_FORMAT) if len(price_data.index) > 0 else "none"
                    }
                }

        # Add ticker information if available from price dataset
            if hasattr(dataset.price_dataset, 'ticker'):
                stats['ticker'] = dataset.price_dataset.ticker

        # Save statistics
        stats_path = os.path.join(output_dir, "dataset_stats.json")
        with open(stats_path, 'w') as f:
            json.dump(stats, f, indent=2)

        logger.info(f"Saved dataset statistics to {stats_path}")

    except Exception as e:
        logger.error(f"Error saving dataset statistics: {e}")
        # Print the full traceback for debugging
        import traceback
        logger.error(traceback.format_exc())


def export_training_data(dataset: IndicatorDataset, output_dir: str, export_format: str = "json"):
    """
    Export training data to a file for inspection.

    Args:
        dataset: IndicatorDataset instance configured for the indicator model.
        output_dir: Directory to save the exported data.
        export_format: Format to export the data in ('json' or 'csv').
    """
    try:
        # Create data directory if it doesn't exist
        data_dir = os.path.join(output_dir, "data")
        os.makedirs(data_dir, exist_ok=True)

        # Prepare data for export
        export_data = []

        # Check if dataset has samples
        if not hasattr(dataset, 'samples') or len(dataset.samples) == 0:
            logger.warning("No samples available for export")

            # Create a placeholder record with debug information
            debug_info = {
                'date': 'no_samples',
                'message': 'No samples available for export',
                'date_range': {
                    'start_date': dataset.start_date if hasattr(dataset, 'start_date') else 'unknown',
                    'end_date': dataset.end_date if hasattr(dataset, 'end_date') else 'unknown'
                }
            }

            # Add news dataset info if available
            if hasattr(dataset, 'news_dataset'):
                news_dataset = dataset.news_dataset
                if hasattr(news_dataset, 'articles_by_date'):
                    debug_info['news_dates_count'] = len(
                        news_dataset.articles_by_date)
                    if news_dataset.articles_by_date:
                        sample_dates = list(
                            news_dataset.articles_by_date.keys())[:5]
                        debug_info['sample_news_dates'] = sample_dates

            # Add price dataset info if available
            if hasattr(dataset, 'price_dataset'):
                price_dataset = dataset.price_dataset
                if hasattr(price_dataset, 'price_data') and not price_dataset.price_data.empty:
                    price_data = price_dataset.price_data
                    debug_info['price_data_count'] = len(price_data)
                    debug_info['price_data_range'] = {
                        'first_date': price_data.index[0].strftime(DATE_FORMAT) if len(price_data.index) > 0 else 'none',
                        'last_date': price_data.index[-1].strftime(DATE_FORMAT) if len(price_data.index) > 0 else 'none'
                    }

            export_data.append(debug_info)
        else:
            # Process each sample
            for sample in dataset.samples:
                article_data = sample.get('article_data', {})

                # Get sentiment data
                sentiment = sample.get('sentiment', 'neutral')
                sentiment_score = sample.get('sentiment_score', 0.0)
                sentiment_scores = sample.get(
                    'sentiment_scores', {'positive': 0.0, 'negative': 0.0, 'neutral': 1.0})

                # Create a simplified record for export
                record = {
                    'date': sample['date'],
                    'next_trading_date': sample['next_trading_date'],
                    'next_day_return': sample['return_value'],
                    'label': sample.get('label', -1),
                    'label_name': sample.get('label_name', 'N/A'),
                    'sentiment': sentiment,
                    'sentiment_score': sentiment_score,
                    'sentiment_positive': sentiment_scores.get('positive', 0.0),
                    'sentiment_negative': sentiment_scores.get('negative', 0.0),
                    'sentiment_neutral': sentiment_scores.get('neutral', 0.0),
                    'title': article_data.get('title', ''),
                    'summary': article_data.get('summary', ''),
                    'url': article_data.get('url', ''),
                    'source': article_data.get('source', ''),
                    'article_text': sample['article_text'],
                    'is_augmented': article_data.get('is_augmented', False),
                    'augmentation_type': article_data.get('augmentation_type', ''),
                    'chunk_type': article_data.get('chunk_type', '')
                }

                export_data.append(record)

        # Export data in the requested format
        if export_format == "json":
            # Save as JSON
            json_path = os.path.join(data_dir, "training_data.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(
                f"Exported {len(export_data)} training samples to {json_path}")

        elif export_format == "csv":
            # Save as CSV
            import csv

            csv_path = os.path.join(data_dir, "training_data.csv")
            with open(csv_path, 'w', encoding='utf-8', newline='') as f:
                # Define CSV fields
                fieldnames = [
                    'date', 'next_trading_date', 'next_day_return', 'label',
                    'label_name', 'sentiment', 'sentiment_score', 'sentiment_positive',
                    'sentiment_negative', 'sentiment_neutral', 'title', 'summary',
                    'url', 'source', 'article_text', 'is_augmented', 'augmentation_type', 'chunk_type'
                ]

                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()

                for record in export_data:
                    writer.writerow(record)

            logger.info(
                f"Exported {len(export_data)} training samples to {csv_path}")

        # Also export a sample file with fewer records and truncated text for quick inspection
        sample_data = export_data[:10] if len(
            export_data) > 10 else export_data

        # Truncate article text for the sample file
        for record in sample_data:
            if len(record['article_text']) > 500:
                record['article_text'] = record['article_text'][:500] + "..."

        sample_path = os.path.join(data_dir, f"sample_data.{export_format}")

        if export_format == "json":
            with open(sample_path, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, indent=2, ensure_ascii=False)
        else:
            import csv
            with open(sample_path, 'w', encoding='utf-8', newline='') as f:
                fieldnames = [
                    'date', 'next_trading_date', 'next_day_return', 'label',
                    'label_name', 'sentiment', 'sentiment_score', 'sentiment_positive',
                    'sentiment_negative', 'sentiment_neutral', 'title', 'summary',
                    'url', 'source', 'article_text', 'is_augmented', 'augmentation_type', 'chunk_type'
                ]
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for record in sample_data:
                    writer.writerow(record)

        logger.info(
            f"Exported {len(sample_data)} sample records to {sample_path}")

    except Exception as e:
        logger.error(f"Error exporting training data: {e}")


def validate_date_format(date_str: str) -> bool:
    """
    Validate that a date string matches the expected format.

    Args:
        date_str: Date string to validate.

    Returns:
        True if the date is valid, False otherwise.
    """
    try:
        # Try to parse the date
        datetime.strptime(date_str, DATE_FORMAT)
        return True
    except ValueError:
        return False


def main():
    """Main function for training the model."""
    args = parse_args()

    # Validate date formats
    if not validate_date_format(args.start_date):
        print(
            f"Error: Start date '{args.start_date}' does not match the expected format '{DATE_FORMAT}'")
        print(f"Please use the format: {DATE_FORMAT}")
        return

    if not validate_date_format(args.end_date):
        print(
            f"Error: End date '{args.end_date}' does not match the expected format '{DATE_FORMAT}'")
        print(f"Please use the format: {DATE_FORMAT}")
        return

    # Validate date range
    try:
        start_dt = datetime.strptime(args.start_date, DATE_FORMAT)
        end_dt = datetime.strptime(args.end_date, DATE_FORMAT)

        if start_dt > end_dt:
            print(
                f"Error: Start date '{args.start_date}' is after end date '{args.end_date}'")
            print("Please provide a valid date range where start_date <= end_date")
            return

        # Check if date range is too large (more than 5 years)
        date_diff = (end_dt - start_dt).days
        if date_diff > 5 * 365:
            print(
                f"Warning: Date range is very large ({date_diff} days, more than 5 years)")
            print(
                "This may cause performance issues or timeout when downloading price data")
            print("Consider using a smaller date range")

        # Check if date range is in the future
        today = datetime.now()
        if end_dt > today:
            print(f"Warning: End date '{args.end_date}' is in the future")
            print("Price data will only be available up to the current date")
    except Exception as e:
        print(f"Error validating date range: {e}")
        return

    # Set up output directory
    output_dir = setup_output_directory(args)

    # Create dataset using IndicatorDataset for the indicator model
    logger.info(
        f"Creating dataset with date range from {args.start_date} to {args.end_date}")
    # Initialize tokenizer
    logger.info(f"Initializing tokenizer with {args.model_name}")
    tokenizer = AutoTokenizer.from_pretrained(args.model_name)

    dataset = IndicatorDataset(
        start_date=args.start_date,
        end_date=args.end_date,
        is_training=True,
        use_augmentation=args.use_augmentation,
        augmentation_techniques=args.augmentation_techniques,
        filter_by_sentiment=args.filter_by_sentiment,
        threshold=args.threshold,
        ticker=args.ticker,
        tokenizer=tokenizer
    )

    # Save original dataset statistics
    logger.info("Saving original dataset statistics")
    original_stats_dir = os.path.join(output_dir, "original_stats")
    os.makedirs(original_stats_dir, exist_ok=True)
    save_dataset_stats(dataset, original_stats_dir)

    # Apply sampling strategy
    if args.sampling_strategy != "none":
        # Apply sampling strategies using the utility function
        dataset.samples = apply_sampling_strategies(
            dataset.samples, args, logger)

        # Save sampled dataset statistics
        logger.info("Saving sampled dataset statistics")
        save_dataset_stats(dataset, output_dir)

    # Export training data if requested
    if args.export_data:
        logger.info(f"Exporting training data in {args.export_format} format")
        export_training_data(dataset, output_dir, args.export_format)
        return

    # Create dataset config for indicator model
    model_config = {
        'model_type': 'indicator',
        'model_name': args.model_name,
        'use_class_weights': args.use_class_weights,
        'class_weights': args.class_weights
    }

    # Initialize model
    logger.info(f"Initializing model with {args.model_name}")
    model = IndicatorModel.from_config(model_config=model_config)

    # Split dataset into training, validation, and test sets using PyTorch's random_split
    # First, split into train+val and test sets
    test_size = int(len(dataset) * args.test_split)
    train_val_size = len(dataset) - test_size

    # Then split train+val into train and validation sets
    val_size = int(train_val_size * args.val_split)
    train_size = train_val_size - val_size

    # Create a generator with a fixed seed for reproducibility
    generator = torch.Generator().manual_seed(42)

    # Split the dataset into train+val and test
    if test_size > 0:
        train_val_dataset, test_dataset = random_split(
            dataset,
            [train_val_size, test_size],
            generator=generator
        )

        # Split train+val into train and validation
        train_dataset, val_dataset = random_split(
            train_val_dataset,
            [train_size, val_size],
            generator=generator
        )

        logger.info(
            f"Split dataset into {train_size} training, {val_size} validation, and {test_size} test samples")
    else:
        # If test_size is 0, just split into train and validation
        train_dataset, val_dataset = random_split(
            dataset,
            [train_size, val_size],
            generator=generator
        )

        logger.info(
            f"Split dataset into {train_size} training and {val_size} validation samples")

    # Train model
    logger.info("Starting model training")
    if test_size > 0:
        metrics = model.train(
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            output_dir=output_dir,
            test_dataset=test_dataset
        )
    else:
        metrics = model.train(
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            output_dir=output_dir
        )

    # Plot metrics
    plot_training_metrics(metrics, output_dir)

    # Print summary
    print("\nTraining Complete!")
    print(f"Model saved to: {output_dir}")
    print(
        f"Data split: {args.test_split*100:.1f}% test, {args.val_split*100:.1f}% validation of remaining data")

    # Print class weights information if used
    if args.use_class_weights:
        print("Used class weights in loss function to handle class imbalance")
        if hasattr(model, 'class_weights') and model.class_weights is not None:
            print(f"  Class weights: {model.class_weights.tolist()}")

    # Print data augmentation details if used
    if args.use_augmentation:
        techniques = args.augmentation_techniques or ["chunk"]
        print(f"Used data augmentation to address class imbalance")
        print(f"  Augmentation techniques: {', '.join(techniques)}")

    # Print sampling strategy details
    if args.sampling_strategy != "none":
        print(f"Used sampling strategy: {args.sampling_strategy}")

        # Get sampling summary using the utility function
        summary = get_sampling_summary(args)

        # Print summary details
        if "keep_positive_negative" in summary:
            print("  Kept all positive and negative samples")

        if "class_ratios" in summary:
            print(f"  Class ratios: {summary['class_ratios']}")

        if "upsample_minority_classes" in summary:
            print("  Upsampled minority classes to match target ratios")

        if "max_samples_per_date" in summary:
            print(f"  Max samples per date: {summary['max_samples_per_date']}")

        if "max_samples_per_source" in summary:
            print(
                f"  Max samples per source: {summary['max_samples_per_source']}")

        if "sentiment_score_threshold" in summary:
            print(
                f"  Sentiment score threshold: {summary['sentiment_score_threshold']}")

    print(f"Accuracy: {metrics['accuracy']:.4f}")
    print("Class Metrics:")
    for label in CLASS_LABELS.values():
        print(f"  {label.capitalize()}:")
        print(f"    Precision: {metrics['precision'][label]:.4f}")
        print(f"    Recall: {metrics['recall'][label]:.4f}")
        print(f"    F1: {metrics['f1'][label]:.4f}")

    # Remind about exported data if applicable
    if args.export_data:
        print(
            f"\nTraining data exported to: {os.path.join(output_dir, 'data')}")
        print(f"Format: {args.export_format}")
        print("Files:")
        print(f"  - training_data.{args.export_format} (full dataset)")
        print(
            f"  - sample_data.{args.export_format} (sample with truncated text)")


if __name__ == "__main__":
    main()
