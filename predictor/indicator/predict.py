"""
<PERSON><PERSON><PERSON> for making predictions with the market movement indicator model.
Uses a classification approach to predict whether an article indicates
positive, negative, or neutral market movement.
"""

# Standard library imports
import json
import argparse
from datetime import datetime
import os
from pathlib import Path
from typing import Dict, Union

# Predictor imports
from predictor.indicator.indicator_model import IndicatorModel
from predictor.config import INDICATOR_MODEL_NAME, ROOT_DIR
from utils.logging_config import get_predictor_logger

# Configure logger for this module
logger = get_predictor_logger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Predict indicators for text input')

    # Model arguments
    parser.add_argument('--model-name', type=str, default=INDICATOR_MODEL_NAME,
                        help=f'Name of the trained model (default: {INDICATOR_MODEL_NAME})')

    # Input arguments
    parser.add_argument('--text', type=str, required=True,
                        help='Text to analyze for market movement prediction')

    # Output arguments
    parser.add_argument('--output-file', type=str,
                        help='Path to save prediction results')

    return parser.parse_args()


def format_prediction_result(
    prediction: Dict[str, Union[str, Dict[str, float]]]
) -> Dict:
    """
    Format prediction result for output.

    Args:
        prediction: Prediction result from the model.

    Returns:
        Formatted prediction result.
    """
    result = {
        "prediction": prediction["prediction"],
        "scores": prediction["scores"],
        "timestamp": datetime.now().isoformat()
    }
    return result


def save_prediction_result(result: Dict, output_file: str):
    """
    Save prediction result to a file.

    Args:
        result: Prediction result.
        output_file: Path to save the result.
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2)
        logger.info(f"Saved prediction result to {output_file}")
    except Exception as e:
        logger.error(f"Error saving prediction result to {output_file}: {e}")


def main():
    """Main function."""
    args = parse_args()
    model_path = Path(os.path.dirname(ROOT_DIR)) / \
        "predictor" / "output" / "models" / args.model_name
    # Load model
    try:
        model = IndicatorModel(model_path=model_path)
    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return

    # Make prediction
    try:
        prediction = model.predict(args.text)
        result = format_prediction_result(prediction)

        # Print prediction
        print("\nPrediction Result:")
        print(f"Market Movement: {result['prediction'].upper()}\n")
        print("Confidence Scores:")
        for label, score in result['scores'].items():
            print(f"  {label.capitalize()}: {score:.4f}")

        # Save result if output file is specified
        if args.output_file:
            save_prediction_result(result, args.output_file)
            print(f"\nResult saved to: {args.output_file}")

    except Exception as e:
        logger.error(f"Error making prediction: {str(e)}")


if __name__ == "__main__":
    main()
